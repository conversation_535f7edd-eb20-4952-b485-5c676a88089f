/**
 * 购物车管理器
 * 负责管理购物车的前端逻辑，包括与后端API的交互
 */
class CartManager {
    constructor() {
        this.cart = [];
        this.listeners = [];
        this.init();
    }

    /**
     * 初始化购物车管理器
     */
    async init() {
        console.log('CartManager.init called');
        // 不在初始化时自动加载购物车，等待用户明确登录后再加载
    }

    /**
     * 获取当前登录用户
     */
    getCurrentUser() {
        try {
            console.log('Getting current user from sessionStorage...');
            const userDataStr = sessionStorage.getItem('loggedInUser');
            console.log('Raw user data from sessionStorage:', userDataStr);

            if (userDataStr) {
                const userData = JSON.parse(userDataStr);
                console.log('Parsed user data:', userData);

                if (userData.isLoggedIn && userData.username) {
                    console.log('Valid user found:', userData.username);
                    return userData;
                } else {
                    console.log('User data invalid - not logged in or no username');
                }
            } else {
                console.log('No user data in sessionStorage');
            }
        } catch (error) {
            console.warn('Failed to get current user:', error);
        }
        return null;
    }

    /**
     * 从服务器加载购物车数据
     */
    async loadCartFromServer() {
        console.log('CartManager.loadCartFromServer called');

        const user = this.getCurrentUser();
        if (!user) {
            console.warn('No logged in user, cannot load cart');
            return;
        }

        console.log('Loading cart for user:', user.username);

        try {
            const url = `/api/cart?username=${encodeURIComponent(user.username)}`;
            console.log('Fetching cart data from:', url);

            const response = await fetch(url);
            console.log('Cart load response status:', response.status);

            const result = await response.json();
            console.log('Cart load response data:', result);

            if (result.success) {
                this.cart = result.data || [];
                console.log('Cart data loaded:', this.cart);
                this.notifyListeners();
            } else {
                console.error('Failed to load cart:', result.message);
            }
        } catch (error) {
            console.error('Error loading cart:', error);
        }
    }

    /**
     * 添加商品到购物车
     */
    async addToCart(productId, quantity = 1) {
        console.log('CartManager.addToCart called with:', { productId, quantity });

        const user = this.getCurrentUser();
        if (!user) {
            console.warn('No user logged in');
            alert('请先登录再添加商品到购物车');
            return false;
        }

        console.log('Current user:', user);

        try {
            const requestData = {
                username: user.username,
                productId: productId,
                quantity: quantity
            };

            console.log('Sending request to /api/cart/add with data:', requestData);

            const response = await fetch('/api/cart/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            const result = await response.json();
            console.log('Response data:', result);

            if (result.success) {
                console.log('Successfully added to cart, reloading cart data...');
                // 重新加载购物车数据
                await this.loadCartFromServer();
                return true;
            } else {
                console.error('Failed to add to cart:', result.message);
                alert(result.message || '添加到购物车失败');
                return false;
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            alert('添加到购物车失败，请稍后重试');
            return false;
        }
    }

    /**
     * 更新购物车商品数量
     */
    async updateQuantity(productId, quantity) {
        const user = this.getCurrentUser();
        if (!user) {
            return false;
        }

        try {
            const response = await fetch('/api/cart/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: user.username,
                    productId: productId,
                    quantity: quantity
                })
            });

            const result = await response.json();
            
            if (result.success) {
                // 重新加载购物车数据
                await this.loadCartFromServer();
                return true;
            } else {
                console.error('Failed to update quantity:', result.message);
                alert(result.message || '更新数量失败');
                return false;
            }
        } catch (error) {
            console.error('Error updating quantity:', error);
            alert('更新数量失败，请稍后重试');
            return false;
        }
    }

    /**
     * 从购物车删除商品
     */
    async removeFromCart(productId) {
        const user = this.getCurrentUser();
        if (!user) {
            return false;
        }

        try {
            const response = await fetch('/api/cart/remove', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: user.username,
                    productId: productId
                })
            });

            const result = await response.json();
            
            if (result.success) {
                // 重新加载购物车数据
                await this.loadCartFromServer();
                return true;
            } else {
                console.error('Failed to remove from cart:', result.message);
                alert(result.message || '删除商品失败');
                return false;
            }
        } catch (error) {
            console.error('Error removing from cart:', error);
            alert('删除商品失败，请稍后重试');
            return false;
        }
    }

    /**
     * 清空购物车
     */
    async clearCart() {
        const user = this.getCurrentUser();
        if (!user) {
            return false;
        }

        if (!confirm('确定要清空购物车吗？')) {
            return false;
        }

        try {
            const response = await fetch('/api/cart/clear', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: user.username
                })
            });

            const result = await response.json();
            
            if (result.success) {
                // 重新加载购物车数据
                await this.loadCartFromServer();
                return true;
            } else {
                console.error('Failed to clear cart:', result.message);
                alert(result.message || '清空购物车失败');
                return false;
            }
        } catch (error) {
            console.error('Error clearing cart:', error);
            alert('清空购物车失败，请稍后重试');
            return false;
        }
    }

    /**
     * 获取购物车商品列表
     */
    getCartItems() {
        return this.cart;
    }

    /**
     * 获取购物车商品总数
     */
    getTotalItems() {
        return this.cart.reduce((total, item) => total + item.quantity, 0);
    }

    /**
     * 获取购物车总价
     */
    getTotalPrice() {
        return this.cart.reduce((total, item) => {
            const price = parseFloat(item.product.price) || 0;
            return total + (price * item.quantity);
        }, 0);
    }

    /**
     * 添加购物车变化监听器
     */
    addListener(callback) {
        this.listeners.push(callback);
    }

    /**
     * 移除购物车变化监听器
     */
    removeListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 通知所有监听器购物车已变化
     */
    notifyListeners() {
        this.listeners.forEach(callback => {
            try {
                callback(this.cart);
            } catch (error) {
                console.error('Error in cart listener:', error);
            }
        });
    }
}

// 创建全局购物车管理器实例的函数
window.initCartManager = function() {
    if (!window.cartManager) {
        console.log('Creating CartManager instance...');
        window.cartManager = new CartManager();
    }
    return window.cartManager;
};

// 如果页面加载时就需要CartManager，创建它
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.initCartManager();
    });
} else {
    window.initCartManager();
}
