const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const Core = require('@alicloud/pop-core');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcrypt');
const multer = require('multer');

// 设置静默模式，减少终端输出
const QUIET_MODE = process.env.QUIET_MODE === 'true';

// 创建简化的控制台输出函数
const quietConsole = {
  log: (message) => {
    if (!QUIET_MODE) {
      console.log(message);
    }
  },
  error: (message) => {
    if (!QUIET_MODE) {
      console.error(message);
    }
  }
};

// 在其他require语句之后添加以下内容
const merchantAuthApi = require('./utils/merchant-auth-api');

const app = express();

// 中间件
app.use(cors());

// 添加请求日志中间件
app.use((req, res, next) => {
  if (req.method === 'PUT' && req.url.includes('/api/products/')) {
    logAdmin(`PUT请求: ${req.url}, Content-Type: ${req.headers['content-type']}`);
  }
  next();
});

app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.use(express.static(path.join(__dirname, '../前端')));

// 创建上传目录
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 配置 multer 用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名：时间戳 + 随机数 + 原始扩展名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB 限制
  },
  fileFilter: function (req, file, cb) {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'));
    }
  }
});

// 视频上传配置
const videoUpload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB 限制
  },
  fileFilter: function (req, file, cb) {
    // 检查文件类型
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传视频文件'), false);
    }
  }
});

// 静态文件服务 - 提供上传的图片访问
app.use('/uploads', express.static(uploadsDir));

// 阿里云邮件服务配置
const directMailClient = new Core({
  accessKeyId: 'LTAI5tCPSjQXRCddvzZX6XxY',
  accessKeySecret: '******************************',
  endpoint: 'https://dm.aliyuncs.com',
  apiVersion: '2015-11-23'
});

// 存储验证码 (生产环境应使用Redis等)
const verificationCodes = new Map();

// 生成随机验证码
function generateCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 记录日志
function logToFile(message, isAdmin = false) {
  const logDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  const now = new Date();
  const dateStr = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
  
  // 根据是否是管理员日志选择不同的文件
  const logFile = path.join(
    logDir, 
    isAdmin ? `admin-${dateStr}.log` : `user-${dateStr}.log`
  );
  
  const logMessage = `[${now.toISOString()}] ${message}\n`;
  
  fs.appendFileSync(logFile, logMessage);
}
// 记录用户日志
function logUser(message) {
  logToFile(message, false);
}

// 记录管理员日志
function logAdmin(message) {
  logToFile(message, true);
}

// 清理旧日志文件 - 保留最近30天的日志
function cleanupOldLogs() {
  try {
    const logDir = path.join(__dirname, 'logs');
    if (!fs.existsSync(logDir)) {
      return;
    }
    
    // 获取当前日期并计算30天前的日期
    const now = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(now.getDate() - 30);
    
    // 读取日志目录下的所有文件
    const files = fs.readdirSync(logDir);
    
    // 检查每个文件，删除30天前的日志
    files.forEach(file => {
      if (file.endsWith('.log')) {
        // 从文件名解析日期 (格式: YYYY-MM-DD.log)
        const dateStr = file.replace('.log', '');
        const [year, month, day] = dateStr.split('-').map(Number);
        const fileDate = new Date(year, month - 1, day); // 月份从0开始
        
        // 如果文件日期早于30天前
        if (fileDate < thirtyDaysAgo) {
          const filePath = path.join(logDir, file);
          fs.unlinkSync(filePath);
          logAdmin(`已删除旧日志文件: ${file}`);
        }
      }
    });
  } catch (error) {
    logAdmin(`清理日志文件时出错: ${error.message}`);
  }
}

// 对验证码进行脱敏处理
function maskCode(code) {
  if (!code) return '';
  if (code.length <= 2) return '*'.repeat(code.length);
  return code.substring(0, 2) + '*'.repeat(code.length - 2);
}

// 清理商品引用 - 从款式组、轮播图等地方移除已删除商品的引用
async function cleanupProductReferences(productId) {
  try {
    // 清理款式组中的引用
    const stylesIndexPath = path.join(__dirname, 'data', 'styles', 'index.json');
    if (fs.existsSync(stylesIndexPath)) {
      const stylesData = JSON.parse(fs.readFileSync(stylesIndexPath, 'utf8'));
      let hasChanges = false;

      stylesData.styles.forEach(style => {
        const originalLength = style.productIds.length;
        style.productIds = style.productIds.filter(id => id !== productId);

        if (style.productIds.length !== originalLength) {
          style.updatedAt = new Date().toISOString();
          hasChanges = true;
          logAdmin(`从款式组 ${style.id} 中移除商品引用: ${productId}`);
        }
      });

      if (hasChanges) {
        fs.writeFileSync(stylesIndexPath, JSON.stringify(stylesData, null, 2));
      }
    }

    // 清理轮播图中的商品引用
    const carouselsPath = path.join(__dirname, 'data', 'carousels.json');
    if (fs.existsSync(carouselsPath)) {
      const carouselsData = JSON.parse(fs.readFileSync(carouselsPath, 'utf8'));
      let hasChanges = false;

      carouselsData.carousels.forEach(carousel => {
        if (carousel.productId === productId) {
          carousel.productId = null;
          carousel.updatedAt = new Date().toISOString();
          hasChanges = true;
          logAdmin(`从轮播图 ${carousel.id} 中移除商品引用: ${productId}`);
        }
      });

      if (hasChanges) {
        fs.writeFileSync(carouselsPath, JSON.stringify(carouselsData, null, 2));
      }
    }

    logAdmin(`商品引用清理完成: ${productId}`);
  } catch (error) {
    logAdmin(`清理商品引用失败: ${productId}, 错误: ${error.message}`);
    throw error;
  }
}

// 获取所有用户
app.get('/api/users', (req, res) => {
  try {
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersFilePath)) {
      logAdmin(`用户数据文件不存在: ${usersFilePath}`);
      return res.json({ success: false, message: '用户数据不存在' });
    }
    
    let users = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));

    // 检查是否需要过滤黑名单用户
    const onlyBlacklisted = req.query.blacklisted === 'true';
    
    // 检查是否只获取今日新增用户
    const onlyToday = req.query.today === 'true';
    
    if (onlyBlacklisted) {
      users = users.filter(user => !!user.blacklisted);
      logAdmin(`管理员获取了黑名单用户列表，共 ${users.length} 条记录`);
    } else if (onlyToday) {
      // 获取今天的开始时间（00:00:00）
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      // 过滤出今天注册的用户
      users = users.filter(user => {
        if (!user.registrationTime) return false;
        const registrationDate = new Date(user.registrationTime);
        return registrationDate >= today;
      });
      
      logAdmin(`管理员获取了今日新增用户列表，共 ${users.length} 条记录`);
    }

    // 格式化用户数据，去除敏感信息
    users = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      registrationTime: user.registrationTime,
      blacklisted: !!user.blacklisted,
      blacklistedTime: user.blacklistedTime || null
    }));
    
    if (!onlyBlacklisted && !onlyToday) {
      logAdmin(`管理员获取了用户列表，共 ${users.length} 条记录`);
    }
    
    return res.json({ success: true, users: users });
  } catch (error) {
    logAdmin(`获取用户列表失败: ${error.message}\n${error.stack}`);
    return res.json({ success: false, message: '获取用户数据失败' });
  }
});

// 拉黑/解除拉黑用户 - 管理员API
app.put('/api/users/:userId/blacklist', (req, res) => {
  try {
    const userId = req.params.userId;
    const { blacklisted } = req.body;
    
    if (blacklisted === undefined) {
      return res.json({ success: false, message: '缺少必要参数' });
    }
    
    // 读取用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersFilePath)) {
      return res.json({ success: false, message: '没有用户数据' });
    }
    
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    let users = JSON.parse(usersData);
    
    // 查找用户
    const userIndex = users.findIndex(user => user.id === userId);
    
    if (userIndex === -1) {
      return res.json({ success: false, message: '用户不存在' });
    }
    
    // 更新用户的拉黑状态
    users[userIndex].blacklisted = blacklisted;
    
    // 如果是拉黑操作，记录拉黑时间
    if (blacklisted) {
      users[userIndex].blacklistedTime = new Date().toISOString();
    } else {
      // 如果是解除拉黑，清除拉黑时间
      users[userIndex].blacklistedTime = null;
    }
    
    // 保存更新后的用户数据
    fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
    
    const actionText = blacklisted ? '拉黑' : '解除拉黑';
    logAdmin(`管理员${actionText}了用户 ${userId}`);
    return res.json({ success: true, message: `用户${actionText}成功` });
  } catch (error) {
    logAdmin(`拉黑/解除拉黑用户失败: ${error.message}\n${error.stack}`);
    return res.json({ success: false, message: '操作失败' });
  }
});

// 获取用户统计数据 - 管理员API
app.get('/api/users-stats', (req, res) => {
  try {
    // 读取用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersFilePath)) {
      return res.json({ success: true, totalUsers: 0, newUsers: 0, blacklistedUsers: 0 });
    }
    
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    const users = JSON.parse(usersData);
    
    // 计算总用户数
    const totalUsers = users.length;
    
    // 计算今日新增用户
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const newUsers = users.filter(user => {
      const registrationDate = new Date(user.registrationTime);
      return registrationDate >= today;
    }).length;
    
    // 计算黑名单用户数
    const blacklistedUsers = users.filter(user => user.blacklisted === true).length;
    
    logAdmin(`管理员获取了用户统计数据: 总用户 ${totalUsers}，今日新增 ${newUsers}，黑名单 ${blacklistedUsers}`);
    return res.json({ 
      success: true, 
      totalUsers: totalUsers, 
      newUsers: newUsers,
      blacklistedUsers: blacklistedUsers
    });
  } catch (error) {
    logAdmin(`获取用户统计数据失败: ${error.message}\n${error.stack}`);
    return res.json({ success: false, message: '获取用户统计数据失败' });
  }
});

// 获取用户注册趋势数据 - 管理员API
app.get('/api/users-registration-trend', (req, res) => {
  try {
    // 获取请求的天数参数，默认为7天
    const days = parseInt(req.query.days) || 7;
    
    // 限制最大查询天数为365天
    const limitedDays = Math.min(days, 365);
    
    // 读取用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersFilePath)) {
      return res.json({ 
        success: true, 
        data: { 
          dates: [], 
          counts: [] 
        } 
      });
    }
    
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    const users = JSON.parse(usersData);
    
    // 计算日期范围
    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999); // 设置为今天结束
    
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - limitedDays + 1);
    startDate.setHours(0, 0, 0, 0); // 设置为起始日开始
    
    // 初始化日期和计数数组
    const dates = [];
    const counts = [];
    
    // 为每一天创建日期字符串和初始计数0
    for (let i = 0; i < limitedDays; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      // 格式化为 MM-DD 格式
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const dateStr = `${month}-${day}`;
      
      dates.push(dateStr);
      counts.push(0);
    }
    
    // 统计每天的注册人数
    users.forEach(user => {
      if (user.registrationTime) {
        const registrationDate = new Date(user.registrationTime);
        
        // 检查是否在查询范围内
        if (registrationDate >= startDate && registrationDate <= endDate) {
          // 计算日期在数组中的索引
          const dayDiff = Math.floor((registrationDate - startDate) / (24 * 60 * 60 * 1000));
          
          if (dayDiff >= 0 && dayDiff < limitedDays) {
            counts[dayDiff]++;
          }
        }
      }
    });
    
    logAdmin(`管理员获取了用户注册趋势数据，查询天数: ${limitedDays}`);
    return res.json({
      success: true,
      data: {
        dates: dates,
        counts: counts
      }
    });
  } catch (error) {
    logAdmin(`获取用户注册趋势数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取用户注册趋势数据失败:', error);
    return res.json({ success: false, message: '获取用户注册趋势数据失败' });
  }
});

// 删除用户 - 管理员API
app.delete('/api/users/:userId', (req, res) => {
  try {
    const userId = req.params.userId;
    
    // 读取用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersFilePath)) {
      return res.json({ success: false, message: '没有用户数据' });
    }
    
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    let users = JSON.parse(usersData);
    
    // 查找用户
    const userIndex = users.findIndex(user => user.id === userId);
    
    if (userIndex === -1) {
      return res.json({ success: false, message: '用户不存在' });
    }
    
    // 从数组中删除用户
    users.splice(userIndex, 1);
    
    // 保存更新后的用户数据
    fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
    
    logAdmin(`管理员删除了用户 ${userId}`);
    return res.json({ success: true, message: '用户删除成功' });
  } catch (error) {
    logAdmin(`删除用户失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除用户失败:', error);
    return res.json({ success: false, message: '删除用户失败' });
  }
});

// API路由
// 发送验证码
app.post('/api/send-verification-code', async (req, res) => {
  try {
    const { email } = req.body;
    
    logUser(`接收到发送验证码请求: ${email}`);
    
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      logUser(`邮箱格式不正确: ${email}`);
      return res.json({ success: false, message: '邮箱格式不正确' });
    }
    
    // 检查邮箱是否被拉黑
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    if (fs.existsSync(usersFilePath)) {
      const usersData = fs.readFileSync(usersFilePath, 'utf8');
      const users = JSON.parse(usersData);
      const user = users.find(u => u.email === email);
      
      if (user && user.blacklisted === true) {
        logUser(`拒绝发送验证码: 邮箱 ${email} 属于被拉黑用户 ${user.username}`);
        return res.json({ 
          success: false, 
          blacklisted: true, 
          message: '你的账号因违规导致封号，如需解锁请联系邮箱********************进行申诉，具体详情参考《服务协议》和《隐私政策》'
        });
      }
    }
    
    // 检查是否已发送验证码且未过期
    const existingVerification = verificationCodes.get(email);
    if (existingVerification && existingVerification.nextSendTime > Date.now()) {
      const remainingSeconds = Math.ceil((existingVerification.nextSendTime - Date.now()) / 1000);
      logUser(`验证码发送过于频繁: ${email}, 剩余 ${remainingSeconds} 秒`);
      return res.json({ success: false, message: `请等待 ${remainingSeconds} 秒后再次发送验证码` });
    }
    
    // 生成6位验证码
    const code = generateCode();
    logUser(`为 ${email} 生成验证码: ${maskCode(code)}`);
    
    // 设置邮件内容
    const params = {
      AccountName: "<EMAIL>", // 阿里云验证过的发信地址
      AddressType: 1,
      ReplyToAddress: false,
      ToAddress: email,
      Subject: "金舟国际物流 - 验证码",
      HtmlBody: `
        <div style="max-width:600px; margin:0 auto; padding:20px; text-align:center; font-family:Arial, sans-serif;">
          <h2 style="color:#0c4da2;">金舟国际物流</h2>
          <h3>验证码</h3>
          <div style="background:#f5f5f5; padding:15px; font-size:24px; letter-spacing:5px; color:#0c4da2; margin:20px 0;">
            ${code}
          </div>
          <p>验证码10分钟内有效，请勿泄露给他人。</p>
          <p style="color:#999; font-size:12px; margin-top:30px;">如果您没有进行此操作，请忽略此邮件</p>
        </div>
      `
    };

    // 调用阿里云DirectMail API发送邮件
    logUser(`尝试发送验证码到: ${email}`);
    const result = await directMailClient.request('SingleSendMail', params, { method: 'POST' });
    logUser(`验证码发送成功: ${JSON.stringify(result)}`);
    
    // 存储验证码，设置10分钟过期
    verificationCodes.set(email, {
      code,
      expiry: Date.now() + 10 * 60 * 1000,
      nextSendTime: Date.now() + 60 * 1000 // 60秒后可再次发送
    });
    
    // 打印当前存储的所有验证码
    logUser(`当前验证码Map中的邮箱: ${Array.from(verificationCodes.keys()).join(', ')}`);
    
    // 延迟删除验证码
    setTimeout(() => {
      const verification = verificationCodes.get(email);
      if (verification && verification.code === code) {
        verificationCodes.delete(email);
        logUser(`验证码过期自动删除: ${email}`);
      }
    }, 10 * 60 * 1000);
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`发送验证码失败: ${error.message}\n${error.stack}`);
    quietConsole.error('发送验证码失败:', error);
    return res.json({ success: false, message: '验证码发送失败，请稍后重试' });
  }
});

// 用户注册
app.post('/api/register', async (req, res) => {
  try {
    const { username, email, verificationCode, password } = req.body;
    
    // 基本输入验证
    if (!username || username.length < 3) {
      return res.json({ success: false, message: '用户名至少需要3个字符' });
    }
    
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.json({ success: false, message: '邮箱格式不正确' });
    }
    
    if (!verificationCode) {
      return res.json({ success: false, message: '请输入验证码' });
    }
    
    if (!password || password.length < 6) {
      return res.json({ success: false, message: '密码至少需要6个字符' });
    }
    
    // 验证码检查
    const storedVerification = verificationCodes.get(email);
    if (!storedVerification) {
      logUser(`注册失败: 未找到该邮箱的验证码: ${email}`);
      return res.json({ success: false, message: '未找到验证码，请重新获取' });
    }
    
    if (storedVerification.code !== verificationCode) {
      logUser(`注册失败: 验证码不匹配: ${email}, 存储的验证码=${maskCode(storedVerification.code)}, 提交的验证码=${maskCode(verificationCode)}`);
      return res.json({ success: false, message: '验证码错误' });
    }
    
    if (storedVerification.expiry < Date.now()) {
      verificationCodes.delete(email);
      logUser(`注册失败: 验证码已过期: ${email}`);
      return res.json({ success: false, message: '验证码已过期，请重新获取' });
    }
    
    // 读取现有用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    let users = [];
    
    try {
      if (fs.existsSync(usersFilePath)) {
        const usersData = fs.readFileSync(usersFilePath, 'utf8');
        users = JSON.parse(usersData);
      }
    } catch (err) {
      logUser(`读取用户数据失败: ${err.message}`);
      // 如果文件不存在或解析出错，则使用空数组
    }
    
    // 检查用户名或邮箱是否已注册
    if (users.some(user => user.username === username)) {
      return res.json({ success: false, message: '用户名已存在，请选择其他用户名' });
    }
    
    if (users.some(user => user.email === email)) {
      return res.json({ success: false, message: '该邮箱已被注册，请使用其他邮箱' });
    }
    
    // 对密码进行加密
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    // 创建新用户对象
    const newUser = {
      id: Date.now().toString(),
      username,
      email,
      password: hashedPassword, // 使用加密后的密码
      registrationTime: new Date().toISOString()
    };
    
    // 添加到用户列表
    users.push(newUser);
    
    // 确保data目录存在
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // 保存到文件
    fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
    
    // 从验证码存储中删除此邮箱记录
    verificationCodes.delete(email);
    
    logUser(`用户注册成功: ${username}, ${email}`);
    return res.json({ success: true });
  } catch (error) {
    logUser(`注册失败: ${error.message}\n${error.stack}`);
    quietConsole.error('注册失败:', error);
    return res.json({ success: false, message: '注册失败，请稍后重试' });
  }
});

// 用户登录
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 基本输入验证
    if (!username || !password) {
      return res.json({ success: false, message: '请输入用户名和密码' });
    }
    
    // 读取用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    if (!fs.existsSync(usersFilePath)) {
      return res.json({ success: false, message: '用户数据库不存在' });
    }
    
    const users = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));
    
    // 通过用户名或邮箱查找用户
    const user = users.find(u => u.username === username || u.email === username);
    
    if (!user) {
      logUser(`登录失败: 用户名或邮箱不存在 - ${username}`);
      return res.json({ success: false, message: '用户名或密码错误' });
    }
    
    // 检查用户是否已被拉黑
    if (user.blacklisted) {
      logUser(`拉黑用户尝试登录: ${username}`);
      return res.json({ 
        success: false, 
        blacklisted: true,
        message: '你的账号因违规导致封号，如需解锁请联系邮箱********************进行申诉，具体详情参考《服务协议》和《隐私政策》'
      });
    }
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      logUser(`登录失败: 密码错误 - ${username}`);
      return res.json({ success: false, message: '用户名或密码错误' });
    }
    
    // 登录成功，返回脱敏的用户信息
    const userInfo = {
      id: user.id,
      username: user.username,
      email: user.email,
      wechat: user.wechat,
      registrationTime: user.registrationTime,
    };
    
    logUser(`用户登录成功: ${username}`);
    return res.json({ 
      success: true,
      user: userInfo
    });
  } catch (error) {
    logUser(`登录失败: ${error.message}\n${error.stack}`);
    quietConsole.error('登录失败:', error);
    return res.json({ success: false, message: '登录失败，请稍后重试' });
  }
});

// 检查用户登录状态
app.post('/api/check-session', (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.json({ success: false, message: '未提供用户ID' });
    }
    
    // 读取用户数据，检查用户是否被拉黑
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    if (fs.existsSync(usersFilePath)) {
      const users = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));
      const user = users.find(u => u.id == userId);
      
      if (user && user.blacklisted) {
        logUser(`已拉黑用户的会话检查: ${userId}`);
        return res.json({ 
          success: false, 
          blacklisted: true,
          forceLogout: true,
          message: '你的账号因违规导致封号，如需解锁请联系邮箱********************进行申诉，具体详情参考《服务协议》和《隐私政策》'
        });
      }
    }
    
    return res.json({ success: true });
  } catch (error) {
    quietConsole.error('会话检查失败:', error);
    return res.json({ success: false, message: '会话检查失败' });
  }
});

// 管理员登录
app.post('/api/admin-login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 基本输入验证
    if (!username || !password) {
      logAdmin(`管理员登录失败: 缺少用户名或密码`);
      return res.json({ success: false, message: '请输入管理员账号和密码' });
    }
    
    // 先检查是否是超级管理员账号
    const adminUsername = "jinzhousyxl888";
    const adminPassword = "cygg888";
    
    // 如果是超级管理员账号，进行验证
    if (username === adminUsername) {
      // 验证管理员密码 - 使用直接比较而不是bcrypt
      const isPasswordValid = (password === adminPassword);
      
      if (!isPasswordValid) {
        logAdmin(`管理员登录失败: 密码错误 - ${username}`);
        return res.json({ success: false, message: '管理员账号或密码错误' });
      }
      
      // 登录成功，返回管理员信息
      const adminInfo = {
        id: "admin-001",
        username: adminUsername
      };
      
      // 生成简单的token（在实际项目中应该使用JWT）
      const token = Buffer.from(adminUsername).toString('base64');

      logAdmin(`管理员账号密码验证成功: ${username}`);
      return res.json({
        success: true,
        admin: adminInfo,
        token: token
      });
    }
    
    // 如果不是超级管理员，则检查是否是客服账号
    const csFilePath = path.join(__dirname, 'data', 'customer-service.json');
    
    if (!fs.existsSync(csFilePath)) {
      logAdmin(`客服登录失败: 客服数据文件不存在`);
      return res.json({ success: false, message: '客服账号或密码错误' });
    }
    
    const csStaff = JSON.parse(fs.readFileSync(csFilePath, 'utf8'));
    
    // 查找匹配的客服账号
    const customer = csStaff.find(cs => cs.account === username);
    
    if (!customer) {
      logAdmin(`客服登录失败: 账号不存在 - ${username}`);
      return res.json({ success: false, message: '客服账号或密码错误' });
    }
    
    // 使用bcrypt验证密码
    const isPasswordValid = await bcrypt.compare(password, customer.password);
    
    if (!isPasswordValid) {
      logAdmin(`客服登录失败: 密码错误 - ${username}`);
      return res.json({ success: false, message: '客服账号或密码错误' });
    }
    
    // 登录成功，返回客服信息（不包含密码）
    const { password: _, ...customerWithoutPassword } = customer;
    
    logAdmin(`客服账号密码验证成功: ${username}`);
    return res.json({ 
      success: true,
      admin: {
        id: customer.id,
        username: customer.account,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        isCustomerService: true
      }
    });
  } catch (error) {
    logAdmin(`登录失败: ${error.message}\n${error.stack}`);
    quietConsole.error('登录失败:', error);
    return res.json({ success: false, message: '登录失败，请稍后重试' });
  }
});

// 管理员token验证API
app.post('/api/admin/verify-token', (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.json({ success: false, message: '缺少token' });
    }

    // 简单的token验证 - 在实际项目中应该使用JWT或其他安全的token机制
    // 这里我们假设token是管理员用户名的base64编码
    try {
      const decoded = Buffer.from(token, 'base64').toString('utf8');
      if (decoded === 'jinzhousyxl888') {
        return res.json({ success: true, message: 'token有效' });
      } else {
        return res.json({ success: false, message: 'token无效' });
      }
    } catch (error) {
      return res.json({ success: false, message: 'token格式错误' });
    }
  } catch (error) {
    logAdmin(`token验证失败: ${error.message}\n${error.stack}`);
    quietConsole.error('token验证失败:', error);
    return res.json({ success: false, message: 'token验证失败' });
  }
});

// 客服专用登录API
app.post('/api/cs-login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 基本输入验证
    if (!username || !password) {
      logAdmin(`客服登录失败: 缺少账号或密码`);
      return res.json({ success: false, message: '请输入客服账号和密码' });
    }
    
    // 读取客服数据文件
    const csFilePath = path.join(__dirname, 'data', 'customer-service.json');
    
    if (!fs.existsSync(csFilePath)) {
      logAdmin(`客服登录失败: 客服数据文件不存在`);
      return res.json({ success: false, message: '客服账号或密码错误' });
    }
    
    const csStaff = JSON.parse(fs.readFileSync(csFilePath, 'utf8'));
    
    // 查找匹配的客服账号
    const customer = csStaff.find(cs => cs.account === username);
    
    if (!customer) {
      logAdmin(`客服登录失败: 账号不存在 - ${username}`);
      return res.json({ success: false, message: '客服账号或密码错误' });
    }
    
    // 使用bcrypt验证密码
    const isPasswordValid = await bcrypt.compare(password, customer.password);
    
    if (!isPasswordValid) {
      logAdmin(`客服登录失败: 密码错误 - ${username}`);
      return res.json({ success: false, message: '客服账号或密码错误' });
    }
    
    // 登录成功，返回客服信息（不包含密码）
    const { password: _, ...customerWithoutPassword } = customer;
    
    logAdmin(`客服账号密码验证成功: ${username}`);
    return res.json({ 
      success: true,
      admin: {
        id: customer.id,
        username: customer.account,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        isCustomerService: true
      }
    });
  } catch (error) {
    logAdmin(`客服登录失败: ${error.message}\n${error.stack}`);
    quietConsole.error('客服登录失败:', error);
    return res.json({ success: false, message: '登录失败，请稍后重试' });
  }
});

// 发送管理员验证码
app.post('/api/send-admin-verification', async (req, res) => {
  try {
    const { username } = req.body;
    
    // 验证是否是管理员账号
    const adminUsername = "jinzhousyxl888";
    if (username !== adminUsername) {
      logAdmin(`发送管理员验证码失败: 非管理员账号 - ${username}`);
      return res.json({ success: false, message: '非管理员账号' });
    }
    
    // 管理员邮箱（固定值）
    const adminEmail = "<EMAIL>";
    
    // 生成6位验证码
    const code = generateCode();
    logAdmin(`为管理员生成验证码: ${maskCode(code)}`);
    
    // 设置邮件内容
    const params = {
      AccountName: "<EMAIL>",
      AddressType: 1,
      ReplyToAddress: false,
      ToAddress: adminEmail,
      Subject: "金舟国际物流 - 管理员登录验证码",
      HtmlBody: `
        <div style="max-width:600px; margin:0 auto; padding:20px; text-align:center; font-family:Arial, sans-serif;">
          <h2 style="color:#0c4da2;">金舟国际物流</h2>
          <h3>管理员登录验证码</h3>
          <div style="background:#f5f5f5; padding:15px; font-size:24px; letter-spacing:5px; color:#0c4da2; margin:20px 0;">
            ${code}
          </div>
          <p>验证码10分钟内有效，请勿泄露给他人。</p>
          <p style="color:#999; font-size:12px; margin-top:30px;">如果您没有进行此操作，请忽略此邮件，并立即修改您的密码</p>
        </div>
      `
    };

    // 调用阿里云DirectMail API发送邮件
    logAdmin(`尝试发送管理员验证码到: ${adminEmail}`);
    
    try {
      const result = await directMailClient.request('SingleSendMail', params, { method: 'POST' });
      logAdmin(`管理员验证码发送成功: ${JSON.stringify(result)}`);
    } catch (mailError) {
      logAdmin(`邮件发送API调用失败: ${mailError.message}，但继续流程`);
      // 开发环境中，即使邮件发送失败也允许继续，方便测试
    }
    
    // 存储验证码，设置10分钟过期
    verificationCodes.set(adminEmail, {
      code,
      expiry: Date.now() + 10 * 60 * 1000,
      nextSendTime: Date.now() + 60 * 1000 // 60秒后可再次发送
    });
    
    // 延迟删除验证码
    setTimeout(() => {
      const verification = verificationCodes.get(adminEmail);
      if (verification && verification.code === code) {
        verificationCodes.delete(adminEmail);
        logAdmin(`管理员验证码过期自动删除`);
      }
    }, 10 * 60 * 1000);
    
    return res.json({ success: true, message: '验证码已发送' });
  } catch (error) {
    logAdmin(`发送管理员验证码失败: ${error.message}\n${error.stack}`);
    quietConsole.error('发送管理员验证码失败:', error);
    return res.json({ success: false, message: '验证码发送失败，请稍后重试' });
  }
});

// 验证管理员验证码
app.post('/api/verify-admin-code', async (req, res) => {
  try {
    const { username, verificationCode } = req.body;
    
    // 验证是否是管理员账号
    const adminUsername = "jinzhousyxl888";
    if (username !== adminUsername) {
      logAdmin(`验证管理员验证码失败: 非管理员账号 - ${username}`);
      return res.json({ success: false, message: '非管理员账号' });
    }
    
    // 管理员邮箱（固定值）
    const adminEmail = "<EMAIL>";
    
    // 验证码检查
    const storedVerification = verificationCodes.get(adminEmail);
    if (!storedVerification) {
      logAdmin(`验证管理员验证码失败: 未找到验证码`);
      return res.json({ success: false, message: '未找到验证码，请重新获取' });
    }
    
    if (storedVerification.code !== verificationCode) {
      logAdmin(`验证管理员验证码失败: 验证码不匹配 - 存储的验证码=${maskCode(storedVerification.code)}, 提交的验证码=${maskCode(verificationCode)}`);
      return res.json({ success: false, message: '验证码错误' });
    }
    
    if (storedVerification.expiry < Date.now()) {
      verificationCodes.delete(adminEmail);
      logAdmin(`验证管理员验证码失败: 验证码已过期`);
      return res.json({ success: false, message: '验证码已过期，请重新获取' });
    }
    
    // 验证成功，删除验证码
    verificationCodes.delete(adminEmail);
    logAdmin(`管理员验证码验证成功: ${username}`);
    
    return res.json({ 
      success: true,
      message: '验证成功'
    });
  } catch (error) {
    logAdmin(`验证管理员验证码失败: ${error.message}\n${error.stack}`);
    quietConsole.error('验证管理员验证码失败:', error);
    return res.json({ success: false, message: '验证失败，请稍后重试' });
  }
});

// 忘记密码 - 验证码验证
app.post('/api/verify-reset-code', (req, res) => {
  try {
    const { email, verificationCode } = req.body;
    
    logUser(`接收到验证重置码请求: 邮箱=${email}, 验证码=${maskCode(verificationCode)}`);
    
    // 基本输入验证
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      logUser(`邮箱格式不正确: ${email}`);
      return res.json({ success: false, message: '邮箱格式不正确' });
    }
    
    // 检查用户是否存在且是否被拉黑
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    if (fs.existsSync(usersFilePath)) {
      const users = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));
      const user = users.find(u => u.email === email);
      
      if (user && user.blacklisted) {
        logUser(`已拉黑用户尝试重置密码: ${email}, 用户ID=${user.id}`);
        return res.json({ 
          success: false, 
          blacklisted: true,
          message: '你的账号因违规导致封号，如需解锁请联系邮箱********************进行申诉，具体详情参考《服务协议》和《隐私政策》'
        });
      }
    }
    
    // 打印验证码存储情况
    logUser(`当前验证码Map中的邮箱: ${Array.from(verificationCodes.keys()).join(', ')}`);
    
    // 验证码检查
    const storedVerification = verificationCodes.get(email);
    if (!storedVerification) {
      logUser(`未找到该邮箱的验证码: ${email}`);
      return res.json({ success: false, message: '未找到验证码，请重新获取' });
    }
    
    if (!verificationCode) {
      logUser(`验证码为空: ${email}`);
      return res.json({ success: false, message: '请输入验证码' });
    }
    
    logUser(`找到验证码: ${email}, 存储的验证码=${maskCode(storedVerification.code)}, 提交的验证码=${maskCode(verificationCode)}`);
    
    if (storedVerification.code !== verificationCode) {
      logUser(`验证码不匹配: ${email}, 存储的验证码=${maskCode(storedVerification.code)}, 提交的验证码=${maskCode(verificationCode)}`);
      return res.json({ success: false, message: '验证码错误' });
    }
    
    if (storedVerification.expiry < Date.now()) {
      logUser(`验证码已过期: ${email}, 过期时间=${new Date(storedVerification.expiry).toISOString()}`);
      verificationCodes.delete(email);
      return res.json({ success: false, message: '验证码已过期，请重新获取' });
    }
    
    // 检查用户是否存在
    const usersJsonPath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersJsonPath)) {
      logUser(`用户数据文件不存在: ${usersJsonPath}`);
      return res.json({ success: false, message: '用户数据库不存在' });
    }
    
    const usersData = fs.readFileSync(usersJsonPath, 'utf8');
    const allUsers = JSON.parse(usersData);
    
    const user = allUsers.find(u => u.email === email);
    
    if (!user) {
      logUser(`密码重置失败: 邮箱不存在 - ${email}`);
      return res.json({ success: false, message: '该邮箱未注册' });
    }
    
    logUser(`密码重置验证码验证成功: ${email}, 用户ID=${user.id}`);
    return res.json({ success: true });
  } catch (error) {
    logUser(`验证重置码失败: ${error.message}\n${error.stack}`);
    quietConsole.error('验证失败:', error);
    return res.json({ success: false, message: '验证失败，请稍后重试' });
  }
});

// 重置密码
app.post('/api/reset-password', async (req, res) => {
  try {
    const { email, verificationCode, newPassword } = req.body;
    
    logUser(`接收到重置密码请求: 邮箱=${email}`);
    
    // 基本输入验证
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      logUser(`邮箱格式不正确: ${email}`);
      return res.json({ success: false, message: '邮箱格式不正确' });
    }
    
    // 检查用户是否被拉黑
    const userFilePath = path.join(__dirname, 'data', 'users.json');
    if (fs.existsSync(userFilePath)) {
      const userData = fs.readFileSync(userFilePath, 'utf8');
      const userList = JSON.parse(userData);
      const userFound = userList.find(u => u.email === email);
      
      if (userFound && userFound.blacklisted === true) {
        logUser(`拒绝重置密码: 邮箱 ${email} 属于被拉黑用户 ${userFound.username}`);
        return res.json({ 
          success: false, 
          blacklisted: true, 
          message: '你的账号因违规导致封号，如需解锁请联系邮箱********************进行申诉，具体详情参考《服务协议》和《隐私政策》'
        });
      }
    }
    
    if (!verificationCode) {
      logUser(`验证码为空: ${email}`);
      return res.json({ success: false, message: '请输入验证码' });
    }
    
    if (!newPassword || newPassword.length < 6) {
      logUser(`密码长度不足: ${email}, 长度=${newPassword ? newPassword.length : 0}`);
      return res.json({ success: false, message: '新密码至少需要6个字符' });
    }
    
    // 验证码检查
    const storedVerification = verificationCodes.get(email);
    if (!storedVerification) {
      logUser(`未找到该邮箱的验证码: ${email}`);
      return res.json({ success: false, message: '未找到验证码，请重新获取' });
    }
    
    logUser(`找到验证码: ${email}, 存储的验证码=${maskCode(storedVerification.code)}, 提交的验证码=${maskCode(verificationCode)}`);
    
    if (storedVerification.code !== verificationCode) {
      logUser(`验证码不匹配: ${email}, 存储的验证码=${maskCode(storedVerification.code)}, 提交的验证码=${maskCode(verificationCode)}`);
      return res.json({ success: false, message: '验证码错误' });
    }
    
    if (storedVerification.expiry < Date.now()) {
      logUser(`验证码已过期: ${email}, 过期时间=${new Date(storedVerification.expiry).toISOString()}`);
      verificationCodes.delete(email);
      return res.json({ success: false, message: '验证码已过期，请重新获取' });
    }
    
    // 读取用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersFilePath)) {
      logUser(`用户数据文件不存在: ${usersFilePath}`);
      return res.json({ success: false, message: '用户数据库不存在' });
    }
    
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    const users = JSON.parse(usersData);
    
    // 查找用户
    const userIndex = users.findIndex(u => u.email === email);
    
    if (userIndex === -1) {
      logUser(`密码重置失败: 邮箱不存在 - ${email}`);
      return res.json({ success: false, message: '该邮箱未注册' });
    }
    
    // 对新密码进行加密
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // 更新用户密码
    users[userIndex].password = hashedPassword;
    
    // 保存到文件
    fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
    
    // 从验证码存储中删除此邮箱记录
    verificationCodes.delete(email);
    
    logUser(`用户成功重置密码: ${email}, 用户ID=${users[userIndex].id}`);
    return res.json({ success: true });
  } catch (error) {
    logUser(`重置密码失败: ${error.message}\n${error.stack}`);
    quietConsole.error('重置密码失败:', error);
    return res.json({ success: false, message: '重置密码失败，请稍后重试' });
  }
});

// 关键词管理API
// 获取所有关键词
app.get('/api/keywords', (req, res) => {
  try {
    const keywordsFilePath = path.join(__dirname, 'data', 'keywords.json');

    if (!fs.existsSync(keywordsFilePath)) {
      // 如果文件不存在，创建一个空的关键词文件
      fs.writeFileSync(keywordsFilePath, JSON.stringify({ keywords: [] }, null, 2));
    }

    const keywordsData = JSON.parse(fs.readFileSync(keywordsFilePath, 'utf8'));

    logAdmin(`获取关键词列表，共 ${keywordsData.keywords.length} 条记录`);
    return res.json({ success: true, keywords: keywordsData.keywords });
  } catch (error) {
    logAdmin(`获取关键词列表失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取关键词列表失败:', error);
    return res.json({ success: false, message: '获取关键词数据失败' });
  }
});

// 添加关键词
app.post('/api/keywords', (req, res) => {
  try {
    const { keyword } = req.body;

    // 基本验证
    if (!keyword || !keyword.trim()) {
      return res.json({ success: false, message: '请输入关键词' });
    }

    const keywordsFilePath = path.join(__dirname, 'data', 'keywords.json');

    // 确保文件存在
    if (!fs.existsSync(keywordsFilePath)) {
      fs.writeFileSync(keywordsFilePath, JSON.stringify({ keywords: [] }, null, 2));
    }

    let keywordsData = JSON.parse(fs.readFileSync(keywordsFilePath, 'utf8'));

    // 检查关键词是否已存在
    if (keywordsData.keywords.some(k => k.text.toLowerCase() === keyword.trim().toLowerCase())) {
      return res.json({ success: false, message: '该关键词已存在' });
    }

    // 创建新关键词对象
    const newKeyword = {
      id: Date.now().toString(),
      text: keyword.trim(),
      addedAt: new Date().toISOString()
    };

    // 添加到关键词列表
    keywordsData.keywords.push(newKeyword);

    // 保存到文件
    fs.writeFileSync(keywordsFilePath, JSON.stringify(keywordsData, null, 2));

    logAdmin(`添加关键词: ${keyword.trim()}`);
    return res.json({ success: true, keyword: newKeyword });
  } catch (error) {
    logAdmin(`添加关键词失败: ${error.message}\n${error.stack}`);
    quietConsole.error('添加关键词失败:', error);
    return res.json({ success: false, message: '添加关键词失败' });
  }
});

// 删除关键词
app.delete('/api/keywords/:id', (req, res) => {
  try {
    const keywordId = req.params.id;

    const keywordsFilePath = path.join(__dirname, 'data', 'keywords.json');

    if (!fs.existsSync(keywordsFilePath)) {
      return res.json({ success: false, message: '关键词数据不存在' });
    }

    let keywordsData = JSON.parse(fs.readFileSync(keywordsFilePath, 'utf8'));

    // 查找关键词
    const keywordIndex = keywordsData.keywords.findIndex(k => k.id === keywordId);

    if (keywordIndex === -1) {
      return res.json({ success: false, message: '关键词不存在' });
    }

    // 获取关键词信息用于日志
    const deletedKeyword = keywordsData.keywords[keywordIndex];

    // 从数组中删除关键词
    keywordsData.keywords.splice(keywordIndex, 1);

    // 保存更新后的关键词数据
    fs.writeFileSync(keywordsFilePath, JSON.stringify(keywordsData, null, 2));

    logAdmin(`删除关键词: ${deletedKeyword.text}`);
    return res.json({ success: true, message: '关键词删除成功' });
  } catch (error) {
    logAdmin(`删除关键词失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除关键词失败:', error);
    return res.json({ success: false, message: '删除关键词失败' });
  }
});

// 商品分类管理API
// 获取所有分类
app.get('/api/categories', (req, res) => {
  try {
    const categoriesFilePath = path.join(__dirname, 'data', 'categories.json');

    if (!fs.existsSync(categoriesFilePath)) {
      // 如果文件不存在，创建一个空的分类文件
      fs.writeFileSync(categoriesFilePath, JSON.stringify({ categories: [] }, null, 2));
    }

    const categoriesData = JSON.parse(fs.readFileSync(categoriesFilePath, 'utf8'));

    logAdmin(`获取分类列表，共 ${categoriesData.categories.length} 条记录`);
    return res.json({ success: true, categories: categoriesData.categories });
  } catch (error) {
    logAdmin(`获取分类列表失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取分类列表失败:', error);
    return res.json({ success: false, message: '获取分类数据失败' });
  }
});

// 添加分类
app.post('/api/categories', (req, res) => {
  try {
    const { name, keywords } = req.body;

    // 基本验证
    if (!name || !name.trim()) {
      return res.json({ success: false, message: '请输入分类名称' });
    }

    const categoriesFilePath = path.join(__dirname, 'data', 'categories.json');

    // 确保文件存在
    if (!fs.existsSync(categoriesFilePath)) {
      fs.writeFileSync(categoriesFilePath, JSON.stringify({ categories: [] }, null, 2));
    }

    let categoriesData = JSON.parse(fs.readFileSync(categoriesFilePath, 'utf8'));

    // 检查分类名称是否已存在
    if (categoriesData.categories.some(c => c.name.toLowerCase() === name.trim().toLowerCase())) {
      return res.json({ success: false, message: '该分类名称已存在' });
    }

    // 创建新分类对象
    const newCategory = {
      id: Date.now().toString(),
      name: name.trim(),
      keywords: keywords || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 添加到分类列表
    categoriesData.categories.push(newCategory);

    // 保存到文件
    fs.writeFileSync(categoriesFilePath, JSON.stringify(categoriesData, null, 2));

    logAdmin(`添加分类: ${name.trim()}, 关键词: ${(keywords || []).join(', ')}`);
    return res.json({ success: true, category: newCategory });
  } catch (error) {
    logAdmin(`添加分类失败: ${error.message}\n${error.stack}`);
    quietConsole.error('添加分类失败:', error);
    return res.json({ success: false, message: '添加分类失败' });
  }
});

// 更新分类
app.put('/api/categories/:id', (req, res) => {
  try {
    const categoryId = req.params.id;
    const { name, keywords } = req.body;

    // 基本验证
    if (!name || !name.trim()) {
      return res.json({ success: false, message: '请输入分类名称' });
    }

    const categoriesFilePath = path.join(__dirname, 'data', 'categories.json');

    if (!fs.existsSync(categoriesFilePath)) {
      return res.json({ success: false, message: '分类数据不存在' });
    }

    let categoriesData = JSON.parse(fs.readFileSync(categoriesFilePath, 'utf8'));

    // 查找分类
    const categoryIndex = categoriesData.categories.findIndex(c => c.id === categoryId);

    if (categoryIndex === -1) {
      return res.json({ success: false, message: '分类不存在' });
    }

    // 检查分类名称是否与其他分类重复
    const existingCategory = categoriesData.categories.find(c =>
      c.id !== categoryId && c.name.toLowerCase() === name.trim().toLowerCase()
    );

    if (existingCategory) {
      return res.json({ success: false, message: '该分类名称已存在' });
    }

    // 更新分类信息
    categoriesData.categories[categoryIndex] = {
      ...categoriesData.categories[categoryIndex],
      name: name.trim(),
      keywords: keywords || [],
      updatedAt: new Date().toISOString()
    };

    // 保存更新后的分类数据
    fs.writeFileSync(categoriesFilePath, JSON.stringify(categoriesData, null, 2));

    logAdmin(`更新分类: ${name.trim()}, 关键词: ${(keywords || []).join(', ')}`);
    return res.json({ success: true, category: categoriesData.categories[categoryIndex] });
  } catch (error) {
    logAdmin(`更新分类失败: ${error.message}\n${error.stack}`);
    quietConsole.error('更新分类失败:', error);
    return res.json({ success: false, message: '更新分类失败' });
  }
});

// 删除分类
app.delete('/api/categories/:id', (req, res) => {
  try {
    const categoryId = req.params.id;

    const categoriesFilePath = path.join(__dirname, 'data', 'categories.json');

    if (!fs.existsSync(categoriesFilePath)) {
      return res.json({ success: false, message: '分类数据不存在' });
    }

    let categoriesData = JSON.parse(fs.readFileSync(categoriesFilePath, 'utf8'));

    // 查找分类
    const categoryIndex = categoriesData.categories.findIndex(c => c.id === categoryId);

    if (categoryIndex === -1) {
      return res.json({ success: false, message: '分类不存在' });
    }

    // 获取分类信息用于日志
    const deletedCategory = categoriesData.categories[categoryIndex];

    // 从数组中删除分类
    categoriesData.categories.splice(categoryIndex, 1);

    // 保存更新后的分类数据
    fs.writeFileSync(categoriesFilePath, JSON.stringify(categoriesData, null, 2));

    logAdmin(`删除分类: ${deletedCategory.name}`);
    return res.json({ success: true, message: '分类删除成功' });
  } catch (error) {
    logAdmin(`删除分类失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除分类失败:', error);
    return res.json({ success: false, message: '删除分类失败' });
  }
});

// 启动服务器
const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  logAdmin(`服务器已启动，监听端口 ${PORT}`);

  // 设置每30天凌晨1点执行一次日志清理
  const setupLogCleanup = () => {
    const now = new Date();
    
    // 判断是否需要立即执行清理
    const lastCleanupStr = getLastCleanupDate();
    let daysToNextCleanup = 30;
    
    if (lastCleanupStr) {
      const lastCleanup = new Date(lastCleanupStr);
      const daysSinceLastCleanup = Math.floor((now - lastCleanup) / (1000 * 60 * 60 * 24));
      daysToNextCleanup = 30 - daysSinceLastCleanup;
      
      if (daysToNextCleanup <= 0) {
        // 已经超过30天，立即执行清理
        daysToNextCleanup = 0;
      }
    }
    
    // 计算下次清理时间
    const nextCleanup = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() + daysToNextCleanup,
      1, 0, 0 // 凌晨1点
    );
    
    // 如果今天已经过了1点，则调整到明天1点
    if (daysToNextCleanup === 0 && now.getHours() >= 1) {
      nextCleanup.setDate(nextCleanup.getDate() + 1);
    }
    
    const timeToNextCleanup = nextCleanup.getTime() - now.getTime();
    
    console.log(`下次日志清理时间: ${nextCleanup.toLocaleString()}, ${Math.floor(timeToNextCleanup / (1000 * 60 * 60 * 24))}天后`);
    logAdmin(`计划下次日志清理时间: ${nextCleanup.toISOString()}`);
    
    // 修复: Node.js的setTimeout在32位系统上最大只能接受约24.8天的延迟
    // 如果需要等待的时间太长，我们分段调度
    const MAX_TIMEOUT = 2147483647; // 最大安全超时值 (约24.8天)
    
    if (timeToNextCleanup <= MAX_TIMEOUT) {
      // 如果时间在安全范围内，直接调度
      setTimeout(() => {
        logAdmin('执行定期日志清理...');
        logAdmin('开始执行30天间隔的定期日志清理');
        cleanupOldLogs();
        saveLastCleanupDate(new Date());
        setupLogCleanup(); // 重新安排下次清理
      }, timeToNextCleanup);
    } else {
      // 如果时间太长，先调度一个最大延迟，然后重新计算
      logAdmin('清理时间超过最大延迟值，将在24天后重新计算');
      setTimeout(() => {
        logAdmin('重新计算下次日志清理时间...');
        setupLogCleanup(); // 重新计算时间
      }, MAX_TIMEOUT);
    }
  };
  
  // 获取上次清理时间
  function getLastCleanupDate() {
    const recordPath = path.join(__dirname, 'logs', 'last_cleanup.txt');
    try {
      if (fs.existsSync(recordPath)) {
        return fs.readFileSync(recordPath, 'utf8');
      }
    } catch (err) {
      quietConsole.error('读取上次清理记录失败:', err);
    }
    return null;
  }
  
  // 保存清理时间
  function saveLastCleanupDate(date) {
    const recordPath = path.join(__dirname, 'logs', 'last_cleanup.txt');
    try {
      fs.writeFileSync(recordPath, date.toISOString());
      logAdmin('已记录本次清理时间');
    } catch (err) {
      logAdmin(`保存清理记录失败: ${err.message}`);
    }
  }
  
  // 启动定时清理系统
  setupLogCleanup();

  // 转换旧日志到新格式
  convertOldLogs();
});

// 添加日志转换函数
function convertOldLogs() {
  const logDir = path.join(__dirname, 'logs');
  const files = fs.readdirSync(logDir).filter(file => 
    !file.startsWith('user-') && 
    !file.startsWith('admin-') && 
    file.endsWith('.log') && 
    file !== 'last_cleanup.txt'
  );
  
  if (files.length === 0) return;
  
  // 是否删除原始文件，可以通过环境变量控制
  const shouldDeleteOriginal = process.env.DELETE_ORIGINAL_LOGS === 'true';
  const isQuietMode = process.env.QUIET_LOG_CONVERSION !== 'false';
  
  // 只在非安静模式下显示开始信息
  if (!isQuietMode) {
    logAdmin('正在转换旧格式日志到新格式...');
  }
  
  let totalUserLogs = 0;
  let totalAdminLogs = 0;
  let convertedFiles = 0;
  
  files.forEach(file => {
    const filePath = path.join(logDir, file);
    try {
      // 只处理尚未被转换的文件
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());
        
        const userLogs = [];
        const adminLogs = [];
        
        lines.forEach(line => {
          if (
            line.includes('管理员') || 
            line.includes('admin') || 
            line.includes('服务器已启动') || 
            line.includes('计划下次日志清理')
          ) {
            adminLogs.push(line);
          } else {
            userLogs.push(line);
          }
        });
        
        // 创建新的用户日志和管理员日志文件
        if (userLogs.length > 0) {
          const userLogPath = path.join(logDir, `user-${file}`);
          fs.writeFileSync(userLogPath, userLogs.join('\n') + '\n');
          totalUserLogs += userLogs.length;
        }
        
        if (adminLogs.length > 0) {
          const adminLogPath = path.join(logDir, `admin-${file}`);
          fs.writeFileSync(adminLogPath, adminLogs.join('\n') + '\n');
          totalAdminLogs += adminLogs.length;
        }
        
        // 根据环境变量决定是否删除原始日志文件
        if (shouldDeleteOriginal) {
          fs.unlinkSync(filePath);
        }
        
        convertedFiles++;
      }
    } catch (error) {
      if (!isQuietMode) {
        quietConsole.error(`转换日志 ${file} 失败:`, error);
      }
    }
  });
  
  // 只在非安静模式下显示完成信息
  if (!isQuietMode) {
    logAdmin(`日志转换完成: 转换了${convertedFiles}个文件，${totalUserLogs}条用户日志，${totalAdminLogs}条管理员日志`);
  }
}

// 创建用户数据存储API
// 用于代替localStorage的数据存储功能

// 确保数据目录存在
function ensureDataDirExists() {
  const dataDir = path.join(__dirname, 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // 确保用户数据目录存在
  const userDataDir = path.join(dataDir, 'userdata');
  if (!fs.existsSync(userDataDir)) {
    fs.mkdirSync(userDataDir, { recursive: true });
  }
  
  return { dataDir, userDataDir };
}

// 获取用户数据目录路径
function getUserDataPath(username) {
  const { userDataDir } = ensureDataDirExists();
  
  // 创建用户专属目录
  const userDir = path.join(userDataDir, username);
  if (!fs.existsSync(userDir)) {
    fs.mkdirSync(userDir, { recursive: true });
  }
  
  return userDir;
}

// 保存表单数据
app.post('/api/save-form-data', async (req, res) => {
  try {
    const { username, formData } = req.body;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const formDataPath = path.join(userDir, 'orderFormData.json');

    // 保存数据到文件
    fs.writeFileSync(formDataPath, JSON.stringify(formData, null, 2));
    logUser(`保存用户表单数据: ${username}`);

    return res.json({ success: true });
  } catch (error) {
    logUser(`保存表单数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存表单数据失败:', error);
    return res.json({ success: false, message: '保存表单数据失败，请稍后重试' });
  }
});

// 保存商品数据（管理员）
app.post('/api/save-product-data', async (req, res) => {
  try {
    const { username, productData } = req.body;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const productDataPath = path.join(userDir, 'productDraft.json');

    // 保存数据到文件
    fs.writeFileSync(productDataPath, JSON.stringify(productData, null, 2));
    logAdmin(`保存管理员商品草稿数据: ${username}`);

    return res.json({ success: true });
  } catch (error) {
    logAdmin(`保存商品草稿数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存商品草稿数据失败:', error);
    return res.json({ success: false, message: '保存商品草稿数据失败，请稍后重试' });
  }
});

// 获取表单数据
app.get('/api/get-form-data', async (req, res) => {
  try {
    const { username } = req.query;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const formDataPath = path.join(userDir, 'orderFormData.json');

    if (fs.existsSync(formDataPath)) {
      const formDataStr = fs.readFileSync(formDataPath, 'utf8');
      return res.json({ success: true, data: JSON.parse(formDataStr) });
    } else {
      return res.json({ success: true, data: null });
    }
  } catch (error) {
    logUser(`获取表单数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取表单数据失败:', error);
    return res.json({ success: false, message: '获取表单数据失败，请稍后重试' });
  }
});

// 获取商品数据（管理员）
app.get('/api/get-product-data', async (req, res) => {
  try {
    const { username } = req.query;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const productDataPath = path.join(userDir, 'productDraft.json');

    if (fs.existsSync(productDataPath)) {
      const productDataStr = fs.readFileSync(productDataPath, 'utf8');
      return res.json({ success: true, data: JSON.parse(productDataStr) });
    } else {
      return res.json({ success: true, data: null });
    }
  } catch (error) {
    logAdmin(`获取商品草稿数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取商品草稿数据失败:', error);
    return res.json({ success: false, message: '获取商品草稿数据失败，请稍后重试' });
  }
});

// 删除表单数据
app.delete('/api/delete-form-data', async (req, res) => {
  try {
    const { username } = req.query;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const formDataPath = path.join(userDir, 'orderFormData.json');

    if (fs.existsSync(formDataPath)) {
      fs.unlinkSync(formDataPath);
      logUser(`删除用户表单数据: ${username}`);
    }

    return res.json({ success: true });
  } catch (error) {
    logUser(`删除表单数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除表单数据失败:', error);
    return res.json({ success: false, message: '删除表单数据失败，请稍后重试' });
  }
});

// 删除商品数据（管理员）
app.delete('/api/delete-product-data', async (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const productDataPath = path.join(userDir, 'productDraft.json');

    if (fs.existsSync(productDataPath)) {
      fs.unlinkSync(productDataPath);
      logAdmin(`删除管理员商品草稿数据: ${username}`);
    }

    return res.json({ success: true });
  } catch (error) {
    logAdmin(`删除商品草稿数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除商品草稿数据失败:', error);
    return res.json({ success: false, message: '删除商品草稿数据失败，请稍后重试' });
  }
});

// 发布商品API
app.post('/api/products', async (req, res) => {
  try {
    const productData = req.body;

    if (!productData.name || !productData.price || !productData.createdBy) {
      return res.json({ success: false, message: '商品信息不完整' });
    }

    // 确保products目录存在
    const productsDir = path.join(__dirname, 'data', 'products');
    if (!fs.existsSync(productsDir)) {
      fs.mkdirSync(productsDir, { recursive: true });
    }

    // 生成唯一的商品ID
    const productId = 'P' + Date.now() + Math.floor(Math.random() * 1000);

    // 添加商品ID、发布时间和初始销量
    const product = {
      ...productData,
      id: productId,
      publishedAt: new Date().toISOString(),
      sales: 0 // 初始销量为0
    };

    // 保存商品数据到文件
    const productFilePath = path.join(productsDir, `${productId}.json`);
    fs.writeFileSync(productFilePath, JSON.stringify(product, null, 2));

    // 更新商品索引文件
    const indexFilePath = path.join(productsDir, 'index.json');
    let productIndex = { products: [] };

    if (fs.existsSync(indexFilePath)) {
      productIndex = JSON.parse(fs.readFileSync(indexFilePath, 'utf8'));
    }

    // 添加到索引
    productIndex.products.unshift({
      id: productId,
      name: product.name,
      price: product.price,
      status: product.status,
      createdBy: product.createdBy,
      publishedAt: product.publishedAt,
      mainImage: product.images && product.images.length > 0 ? product.images[0].url : null,
      sales: product.sales || 0
    });

    // 保存索引文件
    fs.writeFileSync(indexFilePath, JSON.stringify(productIndex, null, 2));

    logAdmin(`商品发布成功: ${product.name} (ID: ${productId}) by ${product.createdBy}`);
    return res.json({ success: true, productId: productId });
  } catch (error) {
    logAdmin(`商品发布失败: ${error.message}\n${error.stack}`);
    quietConsole.error('商品发布失败:', error);
    return res.json({ success: false, message: '商品发布失败，请稍后重试' });
  }
});

// 获取商品列表API
app.get('/api/products', async (req, res) => {
  try {
    const { category, keyword, search } = req.query;
    const productsDir = path.join(__dirname, 'data', 'products');
    const indexFilePath = path.join(productsDir, 'index.json');

    if (!fs.existsSync(indexFilePath)) {
      return res.json({ success: true, products: [] });
    }

    const productIndex = JSON.parse(fs.readFileSync(indexFilePath, 'utf8'));
    let productSummaries = productIndex.products || [];

    // 读取完整的商品数据
    const fullProducts = [];

    for (const productSummary of productSummaries) {
      try {
        const productFilePath = path.join(productsDir, `${productSummary.id}.json`);
        if (fs.existsSync(productFilePath)) {
          const fullProduct = JSON.parse(fs.readFileSync(productFilePath, 'utf8'));
          fullProducts.push(fullProduct);
        }
      } catch (error) {
        quietConsole.error(`读取商品 ${productSummary.id} 失败:`, error);
      }
    }

    let products = fullProducts;

    // 如果有筛选条件，进行筛选
    if (category || keyword || search) {
      const filteredProducts = [];

      for (const fullProduct of products) {
        let shouldInclude = true;

        // 按关键词筛选
        if (keyword) {
          const hasKeyword = fullProduct.keywords && fullProduct.keywords.some(k =>
            k.text.toLowerCase().includes(keyword.toLowerCase())
          );
          if (!hasKeyword) shouldInclude = false;
        }

        // 按搜索词筛选（商品名称或关键词）
        if (search && shouldInclude) {
          const searchLower = search.toLowerCase();
          const nameMatch = fullProduct.name.toLowerCase().includes(searchLower);
          const keywordMatch = fullProduct.keywords && fullProduct.keywords.some(k =>
            k.text.toLowerCase().includes(searchLower)
          );
          if (!nameMatch && !keywordMatch) shouldInclude = false;
        }

        // 按分类筛选（通过分类的关键词）
        if (category && shouldInclude) {
          // 读取分类数据
          const categoriesFilePath = path.join(__dirname, 'data', 'categories.json');
          if (fs.existsSync(categoriesFilePath)) {
            const categoriesData = JSON.parse(fs.readFileSync(categoriesFilePath, 'utf8'));
            const categoryData = categoriesData.categories.find(c => c.id === category);

            if (categoryData && categoryData.keywords) {
              const categoryKeywords = categoryData.keywords.map(k => k.text.toLowerCase());
              const hasMatchingKeyword = fullProduct.keywords && fullProduct.keywords.some(k =>
                categoryKeywords.includes(k.text.toLowerCase())
              );
              if (!hasMatchingKeyword) shouldInclude = false;
            }
          }
        }

        if (shouldInclude) {
          filteredProducts.push(fullProduct);
        }
      }

      products = filteredProducts;
    }

    return res.json({ success: true, products: products });
  } catch (error) {
    logAdmin(`获取商品列表失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取商品列表失败:', error);
    return res.json({ success: false, message: '获取商品列表失败，请稍后重试' });
  }
});

// 获取热销商品列表API
app.get('/api/hot-sales', async (req, res) => {
  try {
    const productsDir = path.join(__dirname, 'data', 'products');
    const indexFilePath = path.join(productsDir, 'index.json');

    if (!fs.existsSync(indexFilePath)) {
      return res.json({ success: true, products: [] });
    }

    const productIndex = JSON.parse(fs.readFileSync(indexFilePath, 'utf8'));
    let products = productIndex.products || [];

    // 为没有销量数据的商品添加随机销量（模拟真实数据）
    products = products.map(product => ({
      ...product,
      sales: product.sales || Math.floor(Math.random() * 2000) + 100
    }));

    // 按销量排序（降序）
    products.sort((a, b) => b.sales - a.sales);

    // 只返回前50名
    const hotSalesProducts = products.slice(0, 50);

    return res.json({ success: true, products: hotSalesProducts });
  } catch (error) {
    logAdmin(`获取热销商品列表失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取热销商品列表失败:', error);
    return res.json({ success: false, message: '获取热销商品列表失败，请稍后重试' });
  }
});

// 获取单个商品详情API
app.get('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const productsDir = path.join(__dirname, 'data', 'products');
    const productFilePath = path.join(productsDir, `${id}.json`);

    if (!fs.existsSync(productFilePath)) {
      return res.json({ success: false, message: '商品不存在' });
    }

    const product = JSON.parse(fs.readFileSync(productFilePath, 'utf8'));
    return res.json({ success: true, product: product });
  } catch (error) {
    logAdmin(`获取商品详情失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取商品详情失败:', error);
    return res.json({ success: false, message: '获取商品详情失败，请稍后重试' });
  }
});

// 更新商品API
app.put('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const productData = req.body;

    logAdmin(`更新商品请求 - ID: ${id}, 商品名: ${productData.name}`);

    if (!productData.name || !productData.price) {
      return res.json({ success: false, message: '商品信息不完整' });
    }

    const productsDir = path.join(__dirname, 'data', 'products');
    const productFilePath = path.join(productsDir, `${id}.json`);
    const indexFilePath = path.join(productsDir, 'index.json');

    // 检查商品是否存在
    if (!fs.existsSync(productFilePath)) {
      return res.json({ success: false, message: '商品不存在' });
    }

    // 读取现有商品数据
    const existingProduct = JSON.parse(fs.readFileSync(productFilePath, 'utf8'));

    // 处理图片更新逻辑
    if (productData.images && Array.isArray(productData.images)) {
      // 获取现有图片列表
      const existingImages = existingProduct.images || [];
      const newImages = productData.images;

      logAdmin(`现有图片: ${existingImages.map(img => img.filename).join(', ')}`);
      logAdmin(`新图片: ${newImages.map(img => img.filename).join(', ')}`);

      // 找出需要删除的图片（存在于旧数据但不在新数据中）
      const imagesToDelete = existingImages.filter(existingImg => {
        // 检查文件名是否在新图片列表中
        const stillExists = newImages.some(newImg => {
          return newImg.filename === existingImg.filename ||
                 newImg.url === existingImg.url ||
                 (newImg.url && existingImg.url && newImg.url.includes(existingImg.filename));
        });
        return !stillExists;
      });

      logAdmin(`需要删除的图片: ${imagesToDelete.map(img => img.filename).join(', ')}`);

      // 删除不再使用的图片文件
      imagesToDelete.forEach(img => {
        if (img.filename) {
          const imagePath = path.join(__dirname, 'uploads', img.filename);
          if (fs.existsSync(imagePath)) {
            try {
              fs.unlinkSync(imagePath);
              logAdmin(`删除商品图片: ${img.filename} (商品ID: ${id})`);
            } catch (error) {
              logAdmin(`删除图片失败: ${img.filename}, 错误: ${error.message}`);
            }
          } else {
            logAdmin(`图片文件不存在，跳过删除: ${img.filename}`);
          }
        }
      });
    }

    // 更新商品数据，保留原有的ID、创建时间、视频等
    const updatedProduct = {
      ...productData,
      id: existingProduct.id,
      publishedAt: existingProduct.publishedAt,
      createdAt: existingProduct.createdAt,
      updatedAt: new Date().toISOString(),
      sales: existingProduct.sales || 0,
      // 保留原有的视频URL，除非前端明确提供了新的视频URL
      videoUrl: productData.videoUrl !== undefined ? productData.videoUrl : existingProduct.videoUrl
    };

    // 保存更新后的商品数据
    fs.writeFileSync(productFilePath, JSON.stringify(updatedProduct, null, 2));

    // 更新商品索引文件
    if (fs.existsSync(indexFilePath)) {
      const productIndex = JSON.parse(fs.readFileSync(indexFilePath, 'utf8'));
      const indexItemIndex = productIndex.products.findIndex(p => p.id === id);

      if (indexItemIndex !== -1) {
        // 更新索引中的商品信息
        productIndex.products[indexItemIndex] = {
          id: updatedProduct.id,
          name: updatedProduct.name,
          price: updatedProduct.price,
          status: updatedProduct.status,
          createdBy: updatedProduct.createdBy,
          publishedAt: updatedProduct.publishedAt,
          mainImage: updatedProduct.images && updatedProduct.images.length > 0 ? updatedProduct.images[0].url : null,
          sales: updatedProduct.sales || 0
        };

        // 保存索引文件
        fs.writeFileSync(indexFilePath, JSON.stringify(productIndex, null, 2));
      }
    }

    logAdmin(`商品更新成功: ${updatedProduct.name} (ID: ${id}) by ${updatedProduct.createdBy}`);
    return res.json({ success: true, product: updatedProduct });
  } catch (error) {
    logAdmin(`商品更新失败: ${error.message}\n${error.stack}`);
    quietConsole.error('商品更新失败:', error);

    // 如果是JSON解析错误，返回更具体的错误信息
    if (error instanceof SyntaxError && error.message.includes('JSON')) {
      return res.json({ success: false, message: 'JSON数据格式错误: ' + error.message });
    }

    return res.json({ success: false, message: '商品更新失败，请稍后重试: ' + error.message });
  }
});

// 更新商品视频API
app.put('/api/products/:id/video', async (req, res) => {
  try {
    const { id } = req.params;
    const { videoUrl, oldVideoUrl } = req.body;

    const productsDir = path.join(__dirname, 'data', 'products');
    const productFilePath = path.join(productsDir, `${id}.json`);

    // 检查商品是否存在
    if (!fs.existsSync(productFilePath)) {
      return res.json({ success: false, message: '商品不存在' });
    }

    // 读取现有商品数据
    const product = JSON.parse(fs.readFileSync(productFilePath, 'utf8'));

    // 获取当前视频URL（如果前端没有提供oldVideoUrl，使用当前的）
    const currentVideoUrl = oldVideoUrl || product.videoUrl;

    // 如果新视频URL与旧视频URL不同，且旧视频是本地文件，删除旧视频文件
    if (currentVideoUrl && currentVideoUrl !== videoUrl && currentVideoUrl.startsWith('/uploads/')) {
      const filename = currentVideoUrl.replace('/uploads/', '');
      const videoPath = path.join(__dirname, 'uploads', filename);
      if (fs.existsSync(videoPath)) {
        try {
          fs.unlinkSync(videoPath);
          logAdmin(`删除旧商品视频文件: ${filename} (商品ID: ${id})`);
        } catch (error) {
          console.warn(`删除旧视频文件失败: ${videoPath}`, error);
        }
      }
    }

    // 更新视频URL
    product.videoUrl = videoUrl || '';
    product.updatedAt = new Date().toISOString();

    // 保存更新后的商品数据
    fs.writeFileSync(productFilePath, JSON.stringify(product, null, 2));

    const action = videoUrl ? (oldVideoUrl ? '更新' : '添加') : '移除';
    logAdmin(`商品视频${action}成功: ${product.name} (ID: ${id}), 视频URL: ${videoUrl || '无'}`);

    res.json({
      success: true,
      message: `商品视频${action}成功`,
      product: product
    });

  } catch (error) {
    console.error('更新商品视频失败:', error);
    res.json({ success: false, message: '更新商品视频失败: ' + error.message });
  }
});

// 删除商品API
app.delete('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const productsDir = path.join(__dirname, 'data', 'products');
    const productFilePath = path.join(productsDir, `${id}.json`);
    const indexFilePath = path.join(productsDir, 'index.json');

    // 检查商品是否存在
    if (!fs.existsSync(productFilePath)) {
      return res.json({ success: false, message: '商品不存在' });
    }

    // 读取商品信息（用于日志）
    const product = JSON.parse(fs.readFileSync(productFilePath, 'utf8'));

    // 删除商品文件
    fs.unlinkSync(productFilePath);

    // 更新索引文件
    if (fs.existsSync(indexFilePath)) {
      const productIndex = JSON.parse(fs.readFileSync(indexFilePath, 'utf8'));
      productIndex.products = productIndex.products.filter(p => p.id !== id);
      fs.writeFileSync(indexFilePath, JSON.stringify(productIndex, null, 2));
    }

    // 删除商品相关的图片文件（如果需要）
    if (product.images && product.images.length > 0) {
      product.images.forEach(image => {
        if (image.filename) {
          const imagePath = path.join(__dirname, 'uploads', image.filename);
          if (fs.existsSync(imagePath)) {
            try {
              fs.unlinkSync(imagePath);
              logAdmin(`删除商品图片: ${image.filename} (商品ID: ${id})`);
            } catch (error) {
              console.warn(`删除图片文件失败: ${imagePath}`, error);
            }
          }
        }
      });
    }

    // 删除商品相关的视频文件（如果需要）
    if (product.videoUrl && product.videoUrl.startsWith('/uploads/')) {
      const videoFilename = product.videoUrl.replace('/uploads/', '');
      const videoPath = path.join(__dirname, 'uploads', videoFilename);
      if (fs.existsSync(videoPath)) {
        try {
          fs.unlinkSync(videoPath);
          logAdmin(`删除商品视频: ${videoFilename} (商品ID: ${id})`);
        } catch (error) {
          console.warn(`删除视频文件失败: ${videoPath}`, error);
        }
      }
    }

    // 清理款式组中的引用
    try {
      await cleanupProductReferences(id);
    } catch (error) {
      console.warn(`清理商品引用失败: ${error.message}`);
    }

    logAdmin(`商品删除成功: ${product.name} (ID: ${id}) by ${product.createdBy || 'unknown'}`);
    return res.json({ success: true, message: '商品删除成功' });
  } catch (error) {
    logAdmin(`商品删除失败: ${error.message}\n${error.stack}`);
    quietConsole.error('商品删除失败:', error);
    return res.json({ success: false, message: '商品删除失败，请稍后重试' });
  }
});

// 款式管理API
const stylesRouter = require('./routes/styles');
app.use('/api/styles', stylesRouter);

// 保存物流码数据
app.post('/api/save-logistics-data', async (req, res) => {
  try {
    const { username, logisticsData } = req.body;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const logisticsDataPath = path.join(userDir, 'logisticsData.json');
    
    // 保存数据到文件
    fs.writeFileSync(logisticsDataPath, JSON.stringify(logisticsData, null, 2));
    logUser(`保存用户物流码数据: ${username}`);
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`保存物流码数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存物流码数据失败:', error);
    return res.json({ success: false, message: '保存物流码数据失败，请稍后重试' });
  }
});

// 获取物流码数据
app.get('/api/get-logistics-data', async (req, res) => {
  try {
    const { username } = req.query;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const logisticsDataPath = path.join(userDir, 'logisticsData.json');
    
    if (fs.existsSync(logisticsDataPath)) {
      const logisticsDataStr = fs.readFileSync(logisticsDataPath, 'utf8');
      return res.json({ success: true, data: JSON.parse(logisticsDataStr) });
    } else {
      return res.json({ success: true, data: null });
    }
  } catch (error) {
    logUser(`获取物流码数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取物流码数据失败:', error);
    return res.json({ success: false, message: '获取物流码数据失败，请稍后重试' });
  }
});

// 删除物流码数据
app.delete('/api/delete-logistics-data', async (req, res) => {
  try {
    const { username } = req.query;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const logisticsDataPath = path.join(userDir, 'logisticsData.json');

    if (fs.existsSync(logisticsDataPath)) {
      fs.unlinkSync(logisticsDataPath);
      logUser(`删除用户物流码数据: ${username}`);
    }

    return res.json({ success: true });
  } catch (error) {
    logUser(`删除物流码数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除物流码数据失败:', error);
    return res.json({ success: false, message: '删除物流码数据失败，请稍后重试' });
  }
});

// 购物车API
// 添加商品到购物车
app.post('/api/cart/add', async (req, res) => {
  try {
    const { username, productId, quantity = 1 } = req.body;

    if (!username || !productId) {
      return res.json({ success: false, message: '缺少必要参数' });
    }

    // 验证商品是否存在
    const productsDir = path.join(__dirname, 'data', 'products');
    const productFilePath = path.join(productsDir, `${productId}.json`);

    if (!fs.existsSync(productFilePath)) {
      return res.json({ success: false, message: '商品不存在' });
    }

    const userDir = getUserDataPath(username);
    const cartPath = path.join(userDir, 'cart.json');

    let cart = [];

    // 读取现有购物车数据
    if (fs.existsSync(cartPath)) {
      const cartStr = fs.readFileSync(cartPath, 'utf8');
      cart = JSON.parse(cartStr);
    }

    // 检查商品是否已在购物车中
    const existingItemIndex = cart.findIndex(item => item.productId === productId);

    if (existingItemIndex >= 0) {
      // 如果商品已存在，增加数量
      cart[existingItemIndex].quantity += quantity;
    } else {
      // 如果商品不存在，添加新项
      cart.push({
        productId,
        quantity,
        addedAt: new Date().toISOString()
      });
    }

    // 保存购物车数据
    fs.writeFileSync(cartPath, JSON.stringify(cart, null, 2));
    logUser(`用户 ${username} 添加商品到购物车: ${productId}, 数量: ${quantity}`);

    return res.json({ success: true, message: '商品已添加到购物车' });
  } catch (error) {
    logUser(`添加商品到购物车失败: ${error.message}\n${error.stack}`);
    quietConsole.error('添加商品到购物车失败:', error);
    return res.json({ success: false, message: '添加商品到购物车失败，请稍后重试' });
  }
});

// 获取购物车商品
app.get('/api/cart', async (req, res) => {
  try {
    const { username } = req.query;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const cartPath = path.join(userDir, 'cart.json');

    if (!fs.existsSync(cartPath)) {
      return res.json({ success: true, data: [] });
    }

    const cartStr = fs.readFileSync(cartPath, 'utf8');
    const cart = JSON.parse(cartStr);

    // 获取商品详细信息
    const productsDir = path.join(__dirname, 'data', 'products');
    const cartWithDetails = [];

    for (const item of cart) {
      const productFilePath = path.join(productsDir, `${item.productId}.json`);

      if (fs.existsSync(productFilePath)) {
        const productStr = fs.readFileSync(productFilePath, 'utf8');
        const product = JSON.parse(productStr);

        cartWithDetails.push({
          ...item,
          product: {
            id: product.id,
            name: product.name,
            price: product.price,
            images: product.images || [],
            description: product.description || ''
          }
        });
      }
    }

    return res.json({ success: true, data: cartWithDetails });
  } catch (error) {
    logUser(`获取购物车数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取购物车数据失败:', error);
    return res.json({ success: false, message: '获取购物车数据失败，请稍后重试' });
  }
});

// 更新购物车商品数量
app.put('/api/cart/update', async (req, res) => {
  try {
    const { username, productId, quantity } = req.body;

    if (!username || !productId || quantity === undefined) {
      return res.json({ success: false, message: '缺少必要参数' });
    }

    if (quantity < 0) {
      return res.json({ success: false, message: '商品数量不能为负数' });
    }

    const userDir = getUserDataPath(username);
    const cartPath = path.join(userDir, 'cart.json');

    if (!fs.existsSync(cartPath)) {
      return res.json({ success: false, message: '购物车为空' });
    }

    const cartStr = fs.readFileSync(cartPath, 'utf8');
    let cart = JSON.parse(cartStr);

    const itemIndex = cart.findIndex(item => item.productId === productId);

    if (itemIndex === -1) {
      return res.json({ success: false, message: '商品不在购物车中' });
    }

    if (quantity === 0) {
      // 如果数量为0，删除商品
      cart.splice(itemIndex, 1);
    } else {
      // 更新数量
      cart[itemIndex].quantity = quantity;
    }

    // 保存购物车数据
    fs.writeFileSync(cartPath, JSON.stringify(cart, null, 2));
    logUser(`用户 ${username} 更新购物车商品数量: ${productId}, 新数量: ${quantity}`);

    return res.json({ success: true, message: '购物车已更新' });
  } catch (error) {
    logUser(`更新购物车失败: ${error.message}\n${error.stack}`);
    quietConsole.error('更新购物车失败:', error);
    return res.json({ success: false, message: '更新购物车失败，请稍后重试' });
  }
});

// 从购物车删除商品
app.delete('/api/cart/remove', async (req, res) => {
  try {
    const { username, productId } = req.body;

    if (!username || !productId) {
      return res.json({ success: false, message: '缺少必要参数' });
    }

    const userDir = getUserDataPath(username);
    const cartPath = path.join(userDir, 'cart.json');

    if (!fs.existsSync(cartPath)) {
      return res.json({ success: false, message: '购物车为空' });
    }

    const cartStr = fs.readFileSync(cartPath, 'utf8');
    let cart = JSON.parse(cartStr);

    const itemIndex = cart.findIndex(item => item.productId === productId);

    if (itemIndex === -1) {
      return res.json({ success: false, message: '商品不在购物车中' });
    }

    // 删除商品
    cart.splice(itemIndex, 1);

    // 保存购物车数据
    fs.writeFileSync(cartPath, JSON.stringify(cart, null, 2));
    logUser(`用户 ${username} 从购物车删除商品: ${productId}`);

    return res.json({ success: true, message: '商品已从购物车删除' });
  } catch (error) {
    logUser(`删除购物车商品失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除购物车商品失败:', error);
    return res.json({ success: false, message: '删除购物车商品失败，请稍后重试' });
  }
});

// 清空购物车
app.delete('/api/cart/clear', async (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const userDir = getUserDataPath(username);
    const cartPath = path.join(userDir, 'cart.json');

    if (fs.existsSync(cartPath)) {
      fs.unlinkSync(cartPath);
      logUser(`用户 ${username} 清空购物车`);
    }

    return res.json({ success: true, message: '购物车已清空' });
  } catch (error) {
    logUser(`清空购物车失败: ${error.message}\n${error.stack}`);
    quietConsole.error('清空购物车失败:', error);
    return res.json({ success: false, message: '清空购物车失败，请稍后重试' });
  }
});

// 保存当前步骤信息
app.post('/api/save-current-step', async (req, res) => {
  try {
    const { username, step } = req.body;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const stepDataPath = path.join(userDir, 'currentStep.json');
    
    // 保存数据到文件
    fs.writeFileSync(stepDataPath, JSON.stringify({ step }, null, 2));
    logUser(`保存用户当前步骤: ${username}, 步骤: ${step}`);
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`保存当前步骤失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存当前步骤失败:', error);
    return res.json({ success: false, message: '保存当前步骤失败，请稍后重试' });
  }
});

// 获取当前步骤信息
app.get('/api/get-current-step', async (req, res) => {
  try {
    const { username } = req.query;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const stepDataPath = path.join(userDir, 'currentStep.json');
    
    if (fs.existsSync(stepDataPath)) {
      const stepDataStr = fs.readFileSync(stepDataPath, 'utf8');
      const stepData = JSON.parse(stepDataStr);
      return res.json({ success: true, step: stepData.step });
    } else {
      return res.json({ success: true, step: null });
    }
  } catch (error) {
    logUser(`获取当前步骤失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取当前步骤失败:', error);
    return res.json({ success: false, message: '获取当前步骤失败，请稍后重试' });
  }
});

// 删除当前步骤信息
app.delete('/api/delete-current-step', async (req, res) => {
  try {
    const { username } = req.query;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const stepDataPath = path.join(userDir, 'currentStep.json');
    
    if (fs.existsSync(stepDataPath)) {
      fs.unlinkSync(stepDataPath);
      logUser(`删除用户当前步骤: ${username}`);
    }
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`删除当前步骤失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除当前步骤失败:', error);
    return res.json({ success: false, message: '删除当前步骤失败，请稍后重试' });
  }
});

// 保存订单数据
app.post('/api/save-orders', async (req, res) => {
  try {
    const { orders } = req.body;
    
    const { dataDir } = ensureDataDirExists();
    const ordersPath = path.join(dataDir, 'orders.json');
    
    // 保存数据到文件
    fs.writeFileSync(ordersPath, JSON.stringify(orders, null, 2));
    logUser(`保存订单数据: ${orders.length} 个订单`);
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`保存订单数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存订单数据失败:', error);
    return res.json({ success: false, message: '保存订单数据失败，请稍后重试' });
  }
});

// 获取订单数据
app.get('/api/get-orders', async (req, res) => {
  try {
    const { dataDir } = ensureDataDirExists();
    const ordersPath = path.join(dataDir, 'orders.json');
    
    if (fs.existsSync(ordersPath)) {
      const ordersStr = fs.readFileSync(ordersPath, 'utf8');
      return res.json({ success: true, data: JSON.parse(ordersStr) });
    } else {
      return res.json({ success: true, data: [] });
    }
  } catch (error) {
    logUser(`获取订单数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取订单数据失败:', error);
    return res.json({ success: false, message: '获取订单数据失败，请稍后重试' });
  }
});

// 添加新订单
app.post('/api/add-order', async (req, res) => {
  try {
    const { order } = req.body;
    
    const { dataDir } = ensureDataDirExists();
    const ordersPath = path.join(dataDir, 'orders.json');
    
    let orders = [];
    
    // 读取现有订单
    if (fs.existsSync(ordersPath)) {
      const ordersStr = fs.readFileSync(ordersPath, 'utf8');
      orders = JSON.parse(ordersStr);
    }
    
    // 添加新订单
    orders.unshift(order); // 添加到最前面，让最新的订单排在前面
    
    // 保存回文件
    fs.writeFileSync(ordersPath, JSON.stringify(orders, null, 2));
    logUser(`添加新订单: ${order.id}, 用户: ${order.username}`);
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`添加订单失败: ${error.message}\n${error.stack}`);
    quietConsole.error('添加订单失败:', error);
    return res.json({ success: false, message: '添加订单失败，请稍后重试' });
  }
});

// 视频上传接口
app.post('/api/upload/video', videoUpload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.json({ success: false, message: '没有上传视频文件' });
    }

    const { productId, oldVideoUrl } = req.body;
    if (!productId) {
      return res.json({ success: false, message: '缺少商品ID' });
    }

    // 如果有旧视频且是本地文件，删除旧视频文件
    if (oldVideoUrl && oldVideoUrl.startsWith('/uploads/')) {
      const oldFilename = oldVideoUrl.replace('/uploads/', '');
      const oldVideoPath = path.join(__dirname, 'uploads', oldFilename);
      if (fs.existsSync(oldVideoPath)) {
        try {
          fs.unlinkSync(oldVideoPath);
          logAdmin(`删除旧视频文件: ${oldFilename} (商品ID: ${productId})`);
        } catch (error) {
          console.warn(`删除旧视频文件失败: ${oldVideoPath}`, error);
        }
      }
    }

    // 构建新视频访问URL
    const videoUrl = `/uploads/${req.file.filename}`;

    logAdmin(`视频上传成功: ${req.file.filename}, 商品ID: ${productId}, 文件大小: ${(req.file.size / 1024 / 1024).toFixed(2)}MB`);

    res.json({
      success: true,
      message: '视频上传成功',
      videoUrl: videoUrl,
      filename: req.file.filename,
      size: req.file.size
    });

  } catch (error) {
    console.error('视频上传失败:', error);
    res.json({ success: false, message: '视频上传失败: ' + error.message });
  }
});

// 文件上传接口
app.post('/api/upload-image', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.json({ success: false, message: '没有上传文件' });
    }

    const { username, logisticsCode } = req.body;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    // 构建文件访问URL
    const fileUrl = `/uploads/${req.file.filename}`;

    // 如果提供了物流码，先检查用户是否已经有与该物流码关联的图片，如果有则删除旧图片
    if (logisticsCode) {
      try {
        const userDir = getUserDataPath(username);
        const logisticsDataPath = path.join(userDir, 'logisticsData.json');
        
        if (fs.existsSync(logisticsDataPath)) {
          // 读取用户的物流数据
          const logisticsDataStr = fs.readFileSync(logisticsDataPath, 'utf8');
          const logisticsData = JSON.parse(logisticsDataStr);
          
          // 查找与当前物流码匹配的数据
          let matchingLogistics = null;
          let codeNumber = null;
          
          // 尝试从格式"物流码X"中提取物流码数字
          const match = logisticsCode.match(/物流码(\d+)/);
          if (match) {
            codeNumber = parseInt(match[1], 10);
          } 
          // 检查是否为纯数字
          else if (/^\d+$/.test(logisticsCode)) {
            codeNumber = parseInt(logisticsCode, 10);
          }
          
          // 先通过code属性尝试精确匹配
          matchingLogistics = logisticsData.find(item => item.code && item.code === logisticsCode);
          
          // 如果没找到且有物流码数字，则通过索引位置匹配
          if (!matchingLogistics && codeNumber !== null) {
            matchingLogistics = logisticsData[codeNumber - 1]; // 索引从0开始，物流码从1开始
          }
          
          // 如果找到匹配的数据，并且有图片文件名，则删除旧图片
          if (matchingLogistics && matchingLogistics.imageFilename) {
            const oldImagePath = path.join(uploadsDir, matchingLogistics.imageFilename);
            
            if (fs.existsSync(oldImagePath)) {
              fs.unlinkSync(oldImagePath);
              logUser(`删除用户 ${username} 的旧图片: ${matchingLogistics.imageFilename}, 替换为新图片: ${req.file.filename}, 物流码: ${logisticsCode}`);
            }
          }
        }
      } catch (error) {
        // 删除旧文件失败不影响上传新文件的结果，只记录错误
        logUser(`删除旧图片失败: ${error.message}\n${error.stack}`);
        quietConsole.error('删除旧图片失败:', error);
      }
    }

    // 记录上传信息
    logUser(`用户 ${username} 上传图片: ${req.file.filename}, 物流码: ${logisticsCode || '未指定'}`);

    return res.json({
      success: true,
      fileUrl: fileUrl,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size
    });
  } catch (error) {
    logUser(`图片上传失败: ${error.message}\n${error.stack}`);
    quietConsole.error('图片上传失败:', error);
    return res.json({ success: false, message: '图片上传失败，请稍后重试' });
  }
});

// 简单保存认证信息的接口
app.post('/api/save-simple-auth', async (req, res) => {
  try {
    const authData = req.body;
    const { username } = authData;

    if (!username) {
      return res.status(400).json({ success: false, message: '未提供用户名' });
    }

    // 确保用户数据目录存在
    const userDir = getUserDataPath(username);
    
    // 构建文件保存路径
    const authFilePath = path.join(userDir, 'merchantAuth.json');
    
    // 直接保存到用户目录
    fs.writeFileSync(authFilePath, JSON.stringify(authData, null, 2));
    
    // 记录日志
    logUser(`用户 ${username} 保存认证信息成功: 公司=${authData.companyName}`);
    
    // 返回成功
    return res.json({ success: true, message: '认证信息保存成功' });
  } catch (error) {
    logUser(`保存认证信息失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存认证信息失败:', error);
    return res.status(500).json({ success: false, message: `保存失败: ${error.message}` });
  }
});

// 删除上传的图片
app.delete('/api/delete-image/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const { username } = req.body;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    const filePath = path.join(uploadsDir, filename);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logUser(`用户 ${username} 删除图片: ${filename}`);
      return res.json({ success: true, message: '图片删除成功' });
    } else {
      return res.json({ success: false, message: '文件不存在' });
    }
  } catch (error) {
    logUser(`删除图片失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除图片失败:', error);
    return res.json({ success: false, message: '删除图片失败，请稍后重试' });
  }
});

// 批量删除上传的图片
app.delete('/api/delete-multiple-images', async (req, res) => {
  try {
    const { username, filenames } = req.body;

    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }

    if (!Array.isArray(filenames) || filenames.length === 0) {
      return res.json({ success: false, message: '未提供有效的文件名列表' });
    }

    let deletedCount = 0;
    const errors = [];

    // 遍历删除每个文件
    for (const filename of filenames) {
      try {
        const filePath = path.join(uploadsDir, filename);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      } catch (err) {
        errors.push({ filename, error: err.message });
      }
    }

    logUser(`用户 ${username} 批量删除图片: 成功删除 ${deletedCount}/${filenames.length} 张图片`);
    
    return res.json({ 
      success: true, 
      message: `成功删除 ${deletedCount}/${filenames.length} 张图片`, 
      deletedCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    logUser(`批量删除图片失败: ${error.message}\n${error.stack}`);
    quietConsole.error('批量删除图片失败:', error);
    return res.json({ success: false, message: '批量删除图片失败，请稍后重试' });
  }
});

// 保存地址数据
app.post('/api/save-addresses', async (req, res) => {
  try {
    const { username, addresses } = req.body;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const addressesPath = path.join(userDir, 'savedAddresses.json');
    
    // 保存数据到文件
    fs.writeFileSync(addressesPath, JSON.stringify(addresses, null, 2));
    logUser(`保存用户地址数据: ${username}, 地址数量: ${addresses.length}`);
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`保存地址数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存地址数据失败:', error);
    return res.json({ success: false, message: '保存地址数据失败，请稍后重试' });
  }
});

// 获取地址数据
app.get('/api/get-addresses', async (req, res) => {
  try {
    const { username } = req.query;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const addressesPath = path.join(userDir, 'savedAddresses.json');
    
    if (fs.existsSync(addressesPath)) {
      const addressesStr = fs.readFileSync(addressesPath, 'utf8');
      return res.json({ success: true, data: JSON.parse(addressesStr) });
    } else {
      return res.json({ success: true, data: [] });
    }
  } catch (error) {
    logUser(`获取地址数据失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取地址数据失败:', error);
    return res.json({ success: false, message: '获取地址数据失败，请稍后重试' });
  }
});

// 保存地址限制提示状态
app.post('/api/save-address-limit-tip-state', async (req, res) => {
  try {
    const { username, state } = req.body;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const tipStatePath = path.join(userDir, 'hideAddressLimitTip.json');
    
    // 保存数据到文件
    fs.writeFileSync(tipStatePath, JSON.stringify({ hide: state }, null, 2));
    logUser(`保存用户地址限制提示状态: ${username}, 状态: ${state}`);
    
    return res.json({ success: true });
  } catch (error) {
    logUser(`保存地址限制提示状态失败: ${error.message}\n${error.stack}`);
    quietConsole.error('保存地址限制提示状态失败:', error);
    return res.json({ success: false, message: '保存地址限制提示状态失败，请稍后重试' });
  }
});

// 获取地址限制提示状态
app.get('/api/get-address-limit-tip-state', async (req, res) => {
  try {
    const { username } = req.query;
    
    if (!username) {
      return res.json({ success: false, message: '未提供用户名' });
    }
    
    const userDir = getUserDataPath(username);
    const tipStatePath = path.join(userDir, 'hideAddressLimitTip.json');
    
    if (fs.existsSync(tipStatePath)) {
      const tipStateStr = fs.readFileSync(tipStatePath, 'utf8');
      const tipState = JSON.parse(tipStateStr);
      return res.json({ success: true, hide: tipState.hide });
    } else {
      return res.json({ success: true, hide: false });
    }
  } catch (error) {
    logUser(`获取地址限制提示状态失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取地址限制提示状态失败:', error);
    return res.json({ success: false, message: '获取地址限制提示状态失败，请稍后重试' });
  }
});

// 保存商家认证信息 (使用merchant-auth-api.js的实现) 

// 获取商家认证信息 (使用merchant-auth-api.js的实现) 

// 检查邮箱是否被拉黑的API
app.post('/api/check-email-blacklist', (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.json({ success: false, message: '邮箱参数缺失' });
    }
    
    logUser(`检查邮箱黑名单状态: ${email}`);
    
    // 读取用户数据
    const usersFilePath = path.join(__dirname, 'data', 'users.json');
    
    if (!fs.existsSync(usersFilePath)) {
      logUser(`检查黑名单: 用户数据文件不存在`);
      return res.json({ success: true, blacklisted: false });
    }
    
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    const users = JSON.parse(usersData);
    
    // 查找与邮箱匹配的用户
    const user = users.find(u => u.email === email);
    
    // 如果找到用户且用户被拉黑
    if (user && user.blacklisted === true) {
      logUser(`邮箱 ${email} 属于被拉黑用户 ${user.username}`);
      return res.json({ 
        success: false, 
        blacklisted: true, 
        message: '你的账号因违规导致封号，如需解锁请联系邮箱********************进行申诉，具体详情参考《服务协议》和《隐私政策》'
      });
    }
    
    // 未找到或用户未被拉黑
    return res.json({ success: true, blacklisted: false });
  } catch (error) {
    logUser(`检查邮箱黑名单状态失败: ${error.message}\n${error.stack}`);
    quietConsole.error('检查邮箱黑名单状态失败:', error);
    return res.json({ success: true, blacklisted: false }); // 默认处理为非黑名单
  }
});

// 商家认证API
merchantAuthApi(app);

// 更新订单状态
app.post('/api/update-order-status', async (req, res) => {
  try {
    const { orderId, status } = req.body;
    
    if (!orderId || !status) {
      return res.json({ success: false, message: '订单ID和状态是必需的' });
    }
    
    const { dataDir } = ensureDataDirExists();
    const ordersPath = path.join(dataDir, 'orders.json');
    
    // 读取现有订单
    if (!fs.existsSync(ordersPath)) {
      return res.json({ success: false, message: '订单数据不存在' });
    }
    
    const ordersStr = fs.readFileSync(ordersPath, 'utf8');
    const orders = JSON.parse(ordersStr);
    
    // 查找指定订单
    const orderIndex = orders.findIndex(o => o.id === orderId);
    
    if (orderIndex === -1) {
      return res.json({ success: false, message: '找不到指定的订单' });
    }
    
    // 更新订单状态
    orders[orderIndex].status = status;
    
    // 保存回文件
    fs.writeFileSync(ordersPath, JSON.stringify(orders, null, 2));
    
    logAdmin(`更新订单 ${orderId} 状态为 ${status}`);
    
    return res.json({ success: true, message: '订单状态已更新' });
  } catch (error) {
    logAdmin(`更新订单状态失败: ${error.message}\n${error.stack}`);
    quietConsole.error('更新订单状态失败:', error);
    return res.json({ success: false, message: '更新订单状态失败，请稍后重试' });
  }
});

// 客服管理API
// 获取所有客服
app.get('/api/customer-service', (req, res) => {
  try {
    const csFilePath = path.join(__dirname, 'data', 'customer-service.json');
    
    if (!fs.existsSync(csFilePath)) {
      return res.json({ success: false, message: '客服数据不存在' });
    }
    
    let csStaff = JSON.parse(fs.readFileSync(csFilePath, 'utf8'));
    
    // 移除密码字段，确保安全
    csStaff = csStaff.map(staff => {
      const { password, ...staffWithoutPassword } = staff;
      return staffWithoutPassword;
    });
    
    logAdmin(`管理员获取了客服列表，共 ${csStaff.length} 条记录`);
    return res.json({ success: true, customerServiceStaff: csStaff });
  } catch (error) {
    logAdmin(`获取客服列表失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取客服列表失败:', error);
    return res.json({ success: false, message: '获取客服数据失败' });
  }
});

// 添加客服
app.post('/api/customer-service', async (req, res) => {
  try {
    const { account, password, name, email, phone, wechat } = req.body;
    
    // 基本验证
    if (!account || !password || !name || !phone) {
      return res.json({ success: false, message: '请填写必要信息' });
    }
    
    const csFilePath = path.join(__dirname, 'data', 'customer-service.json');
    
    // 确保文件存在
    if (!fs.existsSync(csFilePath)) {
      fs.writeFileSync(csFilePath, '[]');
    }
    
    let csStaff = JSON.parse(fs.readFileSync(csFilePath, 'utf8'));
    
    // 检查账号是否已存在
    if (csStaff.some(staff => staff.account === account)) {
      return res.json({ success: false, message: '账号已存在' });
    }
    
    // 生成客服ID
    const lastId = csStaff.length > 0 ? 
      parseInt(csStaff[csStaff.length - 1].id.split('-')[1]) : 0;
    const newId = `CS-${String(lastId + 1).padStart(3, '0')}`;
    
    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // 创建新客服对象
    const newStaff = {
      id: newId,
      account: account,
      password: hashedPassword, // 存储加密后的密码
      name: name,
      email: email || '',
      phone: phone,
      wechat: wechat || '',
      status: 'inactive', // 默认为离线状态
      createdAt: new Date().toISOString()
    };
    
    // 添加到客服数组
    csStaff.push(newStaff);
    
    // 保存到文件
    fs.writeFileSync(csFilePath, JSON.stringify(csStaff, null, 2));
    
    // 返回成功消息，不包含密码
    const { password: _, ...staffWithoutPassword } = newStaff;
    
    logAdmin(`管理员添加了新客服: ${name} (${newId})`);
    return res.json({ 
      success: true, 
      message: '添加客服成功', 
      customerService: staffWithoutPassword 
    });
  } catch (error) {
    logAdmin(`添加客服失败: ${error.message}\n${error.stack}`);
    quietConsole.error('添加客服失败:', error);
    return res.json({ success: false, message: '添加客服失败' });
  }
});

// 删除客服
app.delete('/api/customer-service/:id', (req, res) => {
  try {
    const staffId = req.params.id;
    
    const csFilePath = path.join(__dirname, 'data', 'customer-service.json');
    
    if (!fs.existsSync(csFilePath)) {
      return res.json({ success: false, message: '客服数据不存在' });
    }
    
    let csStaff = JSON.parse(fs.readFileSync(csFilePath, 'utf8'));
    
    // 查找客服
    const staffIndex = csStaff.findIndex(staff => staff.id === staffId);
    
    if (staffIndex === -1) {
      return res.json({ success: false, message: '客服不存在' });
    }
    
    // 记录被删除的客服名称
    const deletedStaffName = csStaff[staffIndex].name;
    
    // 从数组中移除
    csStaff.splice(staffIndex, 1);
    
    // 保存到文件
    fs.writeFileSync(csFilePath, JSON.stringify(csStaff, null, 2));
    
    logAdmin(`管理员删除了客服: ${deletedStaffName} (${staffId})`);
    return res.json({ success: true, message: '删除客服成功' });
  } catch (error) {
    logAdmin(`删除客服失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除客服失败:', error);
    return res.json({ success: false, message: '删除客服失败' });
  }
});

// 编辑客服
app.put('/api/customer-service/:id', async (req, res) => {
  try {
    const staffId = req.params.id;
    const { name, email, phone, wechat, password } = req.body;
    
    const csFilePath = path.join(__dirname, 'data', 'customer-service.json');
    
    if (!fs.existsSync(csFilePath)) {
      return res.json({ success: false, message: '客服数据不存在' });
    }
    
    let csStaff = JSON.parse(fs.readFileSync(csFilePath, 'utf8'));
    
    // 查找客服
    const staffIndex = csStaff.findIndex(staff => staff.id === staffId);
    
    if (staffIndex === -1) {
      return res.json({ success: false, message: '客服不存在' });
    }
    
    // 更新基本信息
    if (name) csStaff[staffIndex].name = name;
    if (email) csStaff[staffIndex].email = email;
    if (phone) csStaff[staffIndex].phone = phone;
    if (wechat !== undefined) csStaff[staffIndex].wechat = wechat;
    
    // 如果提供了新密码，则更新
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      csStaff[staffIndex].password = hashedPassword;
    }
    
    // 添加更新时间
    csStaff[staffIndex].updatedAt = new Date().toISOString();
    
    // 保存到文件
    fs.writeFileSync(csFilePath, JSON.stringify(csStaff, null, 2));
    
    // 返回更新后的客服信息，不包含密码
    const { password: _, ...staffWithoutPassword } = csStaff[staffIndex];
    
    logAdmin(`管理员更新了客服信息: ${csStaff[staffIndex].name} (${staffId})`);
    return res.json({ 
      success: true, 
      message: '更新客服成功', 
      customerService: staffWithoutPassword 
    });
  } catch (error) {
    logAdmin(`更新客服失败: ${error.message}\n${error.stack}`);
    quietConsole.error('更新客服失败:', error);
    return res.json({ success: false, message: '更新客服失败' });
  }
});

// 轮播图管理API
// 获取所有轮播图
app.get('/api/carousels', (req, res) => {
  try {
    const carouselsFilePath = path.join(__dirname, 'data', 'carousels.json');

    if (!fs.existsSync(carouselsFilePath)) {
      // 如果文件不存在，创建一个空的轮播图文件
      const defaultCarousels = { carousels: [] };
      fs.writeFileSync(carouselsFilePath, JSON.stringify(defaultCarousels, null, 2));
      return res.json({ success: true, carousels: [] });
    }

    const carouselsData = JSON.parse(fs.readFileSync(carouselsFilePath, 'utf8'));
    logAdmin(`获取轮播图列表，共 ${carouselsData.carousels.length} 条记录`);
    return res.json({ success: true, carousels: carouselsData.carousels });
  } catch (error) {
    logAdmin(`获取轮播图列表失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取轮播图列表失败:', error);
    return res.json({ success: false, message: '获取轮播图数据失败' });
  }
});

// 获取单个轮播图
app.get('/api/carousels/:id', (req, res) => {
  try {
    const { id } = req.params;
    const carouselsFilePath = path.join(__dirname, 'data', 'carousels.json');

    if (!fs.existsSync(carouselsFilePath)) {
      return res.json({ success: false, message: '轮播图数据不存在' });
    }

    const carouselsData = JSON.parse(fs.readFileSync(carouselsFilePath, 'utf8'));
    const carousel = carouselsData.carousels.find(c => c.id === id);

    if (!carousel) {
      return res.json({ success: false, message: '轮播图不存在' });
    }

    logAdmin(`获取轮播图详情: ${carousel.title} (${id})`);
    return res.json({ success: true, carousel: carousel });
  } catch (error) {
    logAdmin(`获取轮播图详情失败: ${error.message}\n${error.stack}`);
    quietConsole.error('获取轮播图详情失败:', error);
    return res.json({ success: false, message: '获取轮播图详情失败' });
  }
});

// 创建轮播图
app.post('/api/carousels', upload.single('image'), (req, res) => {
  try {
    const { carouselData } = req.body;

    if (!carouselData) {
      return res.json({ success: false, message: '缺少轮播图数据' });
    }

    const carousel = JSON.parse(carouselData);

    if (!carousel.title) {
      return res.json({ success: false, message: '轮播图标题不能为空' });
    }

    // 生成唯一ID
    carousel.id = Date.now().toString();
    carousel.createdAt = new Date().toISOString();
    carousel.updatedAt = new Date().toISOString();

    // 处理上传的图片
    if (req.file) {
      carousel.image = `/uploads/${req.file.filename}`;
    }

    // 读取现有轮播图数据
    const carouselsFilePath = path.join(__dirname, 'data', 'carousels.json');
    let carouselsData = { carousels: [] };

    if (fs.existsSync(carouselsFilePath)) {
      carouselsData = JSON.parse(fs.readFileSync(carouselsFilePath, 'utf8'));
    }

    // 添加新轮播图
    carouselsData.carousels.push(carousel);

    // 保存到文件
    fs.writeFileSync(carouselsFilePath, JSON.stringify(carouselsData, null, 2));

    logAdmin(`创建轮播图: ${carousel.title} (${carousel.id})`);
    return res.json({ success: true, message: '轮播图创建成功', carousel: carousel });
  } catch (error) {
    logAdmin(`创建轮播图失败: ${error.message}\n${error.stack}`);
    quietConsole.error('创建轮播图失败:', error);
    return res.json({ success: false, message: '创建轮播图失败' });
  }
});

// 更新轮播图
app.put('/api/carousels/:id', upload.single('image'), (req, res) => {
  try {
    const { id } = req.params;
    const { carouselData } = req.body;

    if (!carouselData) {
      return res.json({ success: false, message: '缺少轮播图数据' });
    }

    const updatedCarousel = JSON.parse(carouselData);

    if (!updatedCarousel.title) {
      return res.json({ success: false, message: '轮播图标题不能为空' });
    }

    const carouselsFilePath = path.join(__dirname, 'data', 'carousels.json');

    if (!fs.existsSync(carouselsFilePath)) {
      return res.json({ success: false, message: '轮播图数据不存在' });
    }

    const carouselsData = JSON.parse(fs.readFileSync(carouselsFilePath, 'utf8'));
    const carouselIndex = carouselsData.carousels.findIndex(c => c.id === id);

    if (carouselIndex === -1) {
      return res.json({ success: false, message: '轮播图不存在' });
    }

    // 保留原有的创建时间和ID
    const existingCarousel = carouselsData.carousels[carouselIndex];
    updatedCarousel.id = id;
    updatedCarousel.createdAt = existingCarousel.createdAt;
    updatedCarousel.updatedAt = new Date().toISOString();

    // 处理上传的图片
    if (req.file) {
      // 删除旧图片（如果存在）
      if (existingCarousel.image && existingCarousel.image.startsWith('/uploads/')) {
        const oldFilename = existingCarousel.image.replace('/uploads/', '');
        const oldImagePath = path.join(__dirname, 'uploads', oldFilename);
        if (fs.existsSync(oldImagePath)) {
          try {
            fs.unlinkSync(oldImagePath);
            logAdmin(`更新轮播图时删除旧图片: ${oldFilename}`);
          } catch (error) {
            logAdmin(`删除旧轮播图图片失败: ${oldFilename}, 错误: ${error.message}`);
            console.warn(`删除旧图片文件失败: ${oldImagePath}`, error);
          }
        }
      }
      updatedCarousel.image = `/uploads/${req.file.filename}`;
    } else {
      // 保留原有图片
      updatedCarousel.image = existingCarousel.image;
    }

    // 更新轮播图
    carouselsData.carousels[carouselIndex] = updatedCarousel;

    // 保存到文件
    fs.writeFileSync(carouselsFilePath, JSON.stringify(carouselsData, null, 2));

    logAdmin(`更新轮播图: ${updatedCarousel.title} (${id})`);
    return res.json({ success: true, message: '轮播图更新成功', carousel: updatedCarousel });
  } catch (error) {
    logAdmin(`更新轮播图失败: ${error.message}\n${error.stack}`);
    quietConsole.error('更新轮播图失败:', error);
    return res.json({ success: false, message: '更新轮播图失败' });
  }
});

// 删除轮播图
app.delete('/api/carousels/:id', (req, res) => {
  try {
    const { id } = req.params;
    const carouselsFilePath = path.join(__dirname, 'data', 'carousels.json');

    if (!fs.existsSync(carouselsFilePath)) {
      return res.json({ success: false, message: '轮播图数据不存在' });
    }

    const carouselsData = JSON.parse(fs.readFileSync(carouselsFilePath, 'utf8'));
    const carouselIndex = carouselsData.carousels.findIndex(c => c.id === id);

    if (carouselIndex === -1) {
      return res.json({ success: false, message: '轮播图不存在' });
    }

    const deletedCarousel = carouselsData.carousels[carouselIndex];

    // 删除关联的图片文件
    if (deletedCarousel.image && deletedCarousel.image.startsWith('/uploads/')) {
      // 从URL路径中提取文件名
      const filename = deletedCarousel.image.replace('/uploads/', '');
      const imagePath = path.join(__dirname, 'uploads', filename);
      if (fs.existsSync(imagePath)) {
        try {
          fs.unlinkSync(imagePath);
          logAdmin(`删除轮播图关联图片: ${filename}`);
        } catch (error) {
          logAdmin(`删除轮播图图片失败: ${filename}, 错误: ${error.message}`);
          console.warn(`删除图片文件失败: ${imagePath}`, error);
        }
      } else {
        logAdmin(`轮播图图片文件不存在: ${imagePath}`);
      }
    }

    // 从数组中移除
    carouselsData.carousels.splice(carouselIndex, 1);

    // 保存到文件
    fs.writeFileSync(carouselsFilePath, JSON.stringify(carouselsData, null, 2));

    logAdmin(`删除轮播图: ${deletedCarousel.title} (${id})`);
    return res.json({ success: true, message: '轮播图删除成功' });
  } catch (error) {
    logAdmin(`删除轮播图失败: ${error.message}\n${error.stack}`);
    quietConsole.error('删除轮播图失败:', error);
    return res.json({ success: false, message: '删除轮播图失败' });
  }
});