<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的商品 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <script src="js/cart-manager.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo-img {
            width: 45px;
            height: 45px;
            margin-right: 10px;
            border-radius: 8px;
        }

        .logo-text h1 {
            font-size: 22px;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 12px;
            color: #666;
        }

        .gold {
            color: #d4af37;
        }

        /* 欢迎信息和用户下拉菜单相关样式已被移除 */

        /* Header Layout */
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #333;
        }

        .header-title h1 {
            font-size: 22px;
            margin-bottom: 2px;
            color: #0c4da2;
        }

        .header-title h1 i {
            color: #ffd700;
            margin-right: 8px;
        }

        .header-title p {
            font-size: 12px;
            color: #666;
            margin: 0;
        }

        .back-button {
            background: linear-gradient(135deg, #0c4da2 0%, #1e5bb8 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(12, 77, 162, 0.3);
            font-size: 14px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(12, 77, 162, 0.4);
        }

        .back-button i {
            margin-right: 8px;
        }

        /* Header Right Section */
        .header-right {
            display: flex;
            align-items: center;
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 150px);
        }

        /* 新设计 - 选项卡样式 */
        .tabs {
            display: flex;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .tab {
            flex: 1;
            text-align: center;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.8);
            position: relative;
        }

        .tab.active {
            background-color: rgba(255, 255, 255, 0.95);
            color: #0c4da2;
        }

        .tab i {
            margin-right: 8px;
            font-size: 18px;
        }

        .tab-content {
            display: none;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.5s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 商品项目样式 */
        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .product-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s, box-shadow 0.3s;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }

        .product-image {
            height: 180px;
            width: 100%;
            object-fit: cover;
        }

        .product-details {
            padding: 15px;
        }

        .product-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #0c4da2;
        }

        .product-price {
            font-size: 16px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 10px;
        }

        .product-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .product-status {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status-cart {
            background-color: #ffeed9;
            color: #ff8c00;
        }

        .status-paid {
            background-color: #e5f7ed;
            color: #28a745;
        }

        .status-shipped {
            background-color: #e3f2fd;
            color: #0c63e4;
        }

        .product-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: bold;
        }

        .btn-primary {
            background-color: #0c4da2;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0b3d80;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #bb2d3b;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .btn-warning {
            background-color: #fd7e14;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e76b00;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 22px;
            margin-bottom: 10px;
            color: #333;
        }

        .empty-state p {
            font-size: 16px;
            max-width: 400px;
            margin: 0 auto;
        }

        /* 计数器徽章样式 */
        .counter {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #0c4da2;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* 总结部分样式 */
        .summary {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .summary-title {
            font-size: 18px;
            font-weight: bold;
            color: #0c4da2;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #eee;
        }

        .summary-row:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .summary-label {
            color: #666;
        }

        .summary-value {
            font-weight: bold;
            color: #333;
        }

        .summary-total {
            font-size: 18px;
            color: #d4af37;
        }

        /* 结算按钮 */
        .checkout-btn {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
            color: #8b6914;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            margin-top: 20px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
        }

        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(212, 175, 55, 0.4);
        }

        .checkout-btn i {
            margin-right: 8px;
        }

        /* Footer */
        footer {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }

        footer .container {
            max-width: 500px;
        }

        footer p {
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-title h1 {
                font-size: 20px;
            }

            .header-title p {
                font-size: 12px;
            }
            
            .product-list {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }

        @media (max-width: 576px) {
            .header-title {
                display: none;
            }
            
            /* 欢迎信息样式已被移除 */
            
            .product-list {
                grid-template-columns: 1fr;
            }
            
            .tab {
                padding: 10px 5px;
                font-size: 14px;
            }
            
            .tab i {
                margin-right: 5px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-left">
                <div class="logo">
                    <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                    <div class="logo-text">
                        <h1><span class="gold">金舟</span>国际物流</h1>
                        <p>Jin Zhou International Logistics</p>
                    </div>
                </div>
                
                <!-- 欢迎用户按钮已被移除 -->
            </div>

            <div class="header-title">
                <h1><i class="fas fa-box-open"></i>我的商品</h1>
                <p>管理您的商品信息和状态</p>
            </div>

            <div class="header-right">
                <a href="recommend.html" class="back-button">
                    <i class="fas fa-arrow-left"></i>返回推荐
                </a>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <!-- 选项卡导航 -->
            <div class="tabs">
                <div class="tab active" data-tab="cart">
                    <i class="fas fa-shopping-cart"></i>购物车
                </div>
                <div class="tab" data-tab="paid">
                    <i class="fas fa-check-circle"></i>已付款商品
                </div>
            </div>
            
            <!-- 购物车内容 -->
            <div class="tab-content active" id="cart-content">
                <div class="product-list" id="cart-items">
                    <!-- 购物车内容将通过JavaScript动态加载 -->
                </div>
                    
                    <!-- 购物车项目示例 -->
                    <!-- 
                    <div class="product-card">
                        <span class="counter">1</span>
                        <img src="img/product1.jpg" alt="商品图片" class="product-image">
                        <div class="product-details">
                            <span class="product-status status-cart">购物车</span>
                            <h3 class="product-name">高品质旅行箱</h3>
                            <div class="product-price">¥ 399.00</div>
                            <p class="product-description">耐用轻便的旅行箱，适合各种旅行场景，多种颜色可选。</p>
                            <div class="product-actions">
                                <button class="btn btn-danger">删除</button>
                                <button class="btn btn-success">立即购买</button>
                            </div>
                        </div>
                    </div>
                    -->
                </div>
                
                <!-- 购物车结算区域 -->
                <div class="summary" id="cart-summary">
                    <h3 class="summary-title">购物车汇总</h3>
                    <div class="summary-row">
                        <span class="summary-label">商品数量</span>
                        <span class="summary-value" id="cart-item-count">0</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">商品总价</span>
                        <span class="summary-value summary-total" id="cart-total-price">¥ 0.00</span>
                    </div>
                    <button class="checkout-btn" id="checkout-btn">
                        <i class="fas fa-credit-card"></i>结算全部
                    </button>
                </div>
            </div>
            
            <!-- 已付款商品内容 -->
            <div class="tab-content" id="paid-content">
                <div class="product-list" id="paid-items">
                    <!-- 没有已付款商品的提示 -->
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>暂无已付款商品</h3>
                        <p>您的已付款商品将显示在这里</p>
                    </div>
                    
                    <!-- 已付款商品示例 -->
                    <!--
                    <div class="product-card">
                        <img src="img/product2.jpg" alt="商品图片" class="product-image">
                        <div class="product-details">
                            <span class="product-status status-shipped">已发货</span>
                            <h3 class="product-name">智能手表</h3>
                            <div class="product-price">¥ 899.00</div>
                            <p class="product-description">多功能智能手表，支持心率监测、运动跟踪等功能。</p>
                            <div class="product-actions">
                                <button class="btn btn-primary">查看详情</button>
                                <button class="btn btn-warning">查询物流</button>
                            </div>
                        </div>
                    </div>
                    -->
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container" style="max-width: 800px;">
            <p>&copy; 2023 <span class="gold">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // 检查用户登录状态
        document.addEventListener('DOMContentLoaded', function() {
            // 欢迎用户按钮已被移除，相关变量也已移除
            
            // 选项卡切换逻辑
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有选项卡的活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    // 给当前选项卡添加活动状态
                    tab.classList.add('active');
                    
                    // 隐藏所有内容
                    tabContents.forEach(content => content.classList.remove('active'));
                    // 显示对应内容
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(`${tabId}-content`).classList.add('active');
                });
            });
            
            // 安全地获取登录信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data from sessionStorage:', error);
                sessionStorage.removeItem('loggedInUser');
            }

            // 检查用户是否登录，如果未登录则重定向到登录页面
            if (!loggedInUser || !loggedInUser.isLoggedIn || !loggedInUser.username) {
                // 用户未登录，重定向到登录页面
                window.location.href = 'login.html?returnUrl=' + encodeURIComponent(window.location.href);
                return;
            }

            // 用户已登录，更新欢迎信息
            // 检查用户是否被拉黑
            BlacklistHandler.checkUserBlacklisted();

            // 定期检查用户状态
            setInterval(BlacklistHandler.checkUserBlacklisted, 30000);

            // 更新欢迎信息
            loginStatusText.textContent = '欢迎，' + loggedInUser.username;
            welcomeMessage.classList.remove('not-logged-in');

            // 更新下拉菜单中的用户名
            accountName.textContent = "我的账户";

            // 删除直接跳转到用户中心的点击事件
            welcomeMessage.style.cursor = 'pointer';

            // 为"我的账户"添加点击事件，跳转到用户中心
            document.querySelector('.account-info').addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = 'dashboard.html'; // 假设用户中心页面是dashboard.html
            });

            // 处理退出登录
            logoutButton.addEventListener('click', function(e) {
                e.preventDefault();
                sessionStorage.removeItem('loggedInUser');
                window.location.href = 'recommend.html';
            });

            // 用户登录后初始化购物车显示
            console.log('User logged in, initializing cart display...');
            initializeCartDisplay();

            // 结算按钮事件
            const checkoutBtn = document.getElementById('checkout-btn');
            if (checkoutBtn) {
                checkoutBtn.addEventListener('click', function() {
                    // 这里可以添加结算逻辑
                    alert('结算功能将在后续开发中实现');
                });
            }
        });

        // 购物车显示逻辑
        function initializeCartDisplay() {
            console.log('initializeCartDisplay called');

            // 确保CartManager已初始化
            if (window.initCartManager) {
                window.initCartManager();
            }

            if (window.cartManager) {
                console.log('CartManager found, setting up listener');

                // 监听购物车变化
                window.cartManager.addListener(updateCartDisplay);

                // 强制重新加载购物车数据
                setTimeout(async () => {
                    console.log('Force reloading cart data...');
                    await window.cartManager.loadCartFromServer();
                    const cartItems = window.cartManager.getCartItems();
                    console.log('Reloaded cart items:', cartItems);
                    updateCartDisplay(cartItems);
                }, 1000);
            } else {
                console.error('CartManager not found!');
            }
        }

        function updateCartDisplay(cartItems) {
            console.log('updateCartDisplay called with:', cartItems);

            const cartItemsContainer = document.getElementById('cart-items');
            const cartItemCount = document.getElementById('cart-item-count');
            const cartTotalPrice = document.getElementById('cart-total-price');

            console.log('Cart container found:', !!cartItemsContainer);
            console.log('Cart item count element found:', !!cartItemCount);
            console.log('Cart total price element found:', !!cartTotalPrice);

            if (!cartItemsContainer) {
                console.error('Cart items container not found!');
                return;
            }

            if (!cartItems || cartItems.length === 0) {
                // 显示空购物车状态
                console.log('Displaying empty cart state');
                cartItemsContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>您的购物车是空的</h3>
                        <p>浏览商品推荐，将心仪的商品添加到购物车</p>
                    </div>
                `;

                if (cartItemCount) cartItemCount.textContent = '0';
                if (cartTotalPrice) cartTotalPrice.textContent = '¥ 0.00';
            } else {
                console.log('Displaying cart items, count:', cartItems.length);
                // 显示购物车商品
                let totalItems = 0;
                let totalPrice = 0;

                const cartHTML = cartItems.map(item => {
                    const product = item.product;
                    const quantity = item.quantity;
                    const price = parseFloat(product.price) || 0;
                    const itemTotal = price * quantity;

                    totalItems += quantity;
                    totalPrice += itemTotal;

                    const imageUrl = product.images && product.images.length > 0
                        ? product.images[0]
                        : 'img/default-product.jpg';

                    return `
                        <div class="product-card" data-product-id="${product.id}">
                            <span class="counter">${totalItems}</span>
                            <img src="${imageUrl}" alt="${product.name}" class="product-image" onerror="this.src='img/default-product.jpg'">
                            <div class="product-details">
                                <span class="product-status status-cart">购物车</span>
                                <h3 class="product-name">${product.name}</h3>
                                <div class="product-price">¥ ${price.toFixed(2)}</div>
                                <p class="product-description">${product.description || '暂无描述'}</p>
                                <div class="quantity-controls" style="margin: 10px 0;">
                                    <button class="quantity-btn" onclick="updateQuantity('${product.id}', ${quantity - 1})">-</button>
                                    <span class="quantity-display">${quantity}</span>
                                    <button class="quantity-btn" onclick="updateQuantity('${product.id}', ${quantity + 1})">+</button>
                                    <span class="item-total" style="margin-left: 15px; font-weight: bold;">小计: ¥${itemTotal.toFixed(2)}</span>
                                </div>
                                <div class="product-actions">
                                    <button class="btn btn-danger" onclick="removeFromCart('${product.id}')">删除</button>
                                    <button class="btn btn-success" onclick="buyNow('${product.id}')">立即购买</button>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                cartItemsContainer.innerHTML = cartHTML;

                if (cartItemCount) cartItemCount.textContent = totalItems.toString();
                if (cartTotalPrice) cartTotalPrice.textContent = `¥ ${totalPrice.toFixed(2)}`;
            }
        }

        // 更新商品数量
        async function updateQuantity(productId, newQuantity) {
            if (newQuantity < 0) return;

            if (window.cartManager) {
                await window.cartManager.updateQuantity(productId, newQuantity);
            }
        }

        // 从购物车删除商品
        async function removeFromCart(productId) {
            if (confirm('确定要删除这个商品吗？')) {
                if (window.cartManager) {
                    await window.cartManager.removeFromCart(productId);
                }
            }
        }

        // 立即购买
        function buyNow(productId) {
            alert('立即购买功能将在后续开发中实现');
        }
    </script>

    <style>
        /* 购物车商品卡片样式 */
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
        }

        .quantity-btn:hover {
            background: #f5f5f5;
        }

        .quantity-display {
            min-width: 40px;
            text-align: center;
            font-weight: bold;
        }

        .item-total {
            color: #ff6b35;
            font-size: 14px;
        }

        .counter {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #ff6b35;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</body>
</html>