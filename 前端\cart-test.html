<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车测试页面</title>
    <script src="js/cart-manager.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .cart-items {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>购物车功能测试</h1>
    
    <div class="test-section">
        <h2>1. 用户登录测试</h2>
        <button onclick="simulateLogin()">模拟用户登录</button>
        <button onclick="checkLoginStatus()">检查登录状态</button>
        <button onclick="logout()">退出登录</button>
        <div id="login-status" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 购物车操作测试</h2>
        <button onclick="addTestProduct()">添加测试商品</button>
        <button onclick="loadCart()">加载购物车</button>
        <button onclick="clearCart()">清空购物车</button>
        <div id="cart-status" class="log"></div>
        <div id="cart-items" class="cart-items"></div>
    </div>
    
    <div class="test-section">
        <h2>3. API测试</h2>
        <button onclick="testAddAPI()">测试添加API</button>
        <button onclick="testGetAPI()">测试获取API</button>
        <div id="api-status" class="log"></div>
    </div>

    <script>
        // 模拟用户登录
        function simulateLogin() {
            const userData = {
                username: 'testuser',
                isLoggedIn: true,
                loginTime: new Date().toISOString()
            };
            sessionStorage.setItem('loggedInUser', JSON.stringify(userData));
            updateLoginStatus('用户已登录: ' + userData.username);
            
            // 重新初始化CartManager
            if (window.initCartManager) {
                window.initCartManager();
            }
        }
        
        // 检查登录状态
        function checkLoginStatus() {
            const userDataStr = sessionStorage.getItem('loggedInUser');
            if (userDataStr) {
                const userData = JSON.parse(userDataStr);
                updateLoginStatus('当前用户: ' + JSON.stringify(userData, null, 2));
            } else {
                updateLoginStatus('未登录');
            }
        }
        
        // 退出登录
        function logout() {
            sessionStorage.removeItem('loggedInUser');
            updateLoginStatus('已退出登录');
        }
        
        // 添加测试商品
        async function addTestProduct() {
            if (!window.cartManager) {
                updateCartStatus('CartManager未初始化');
                return;
            }

            try {
                // 使用真实的商品ID
                const result = await window.cartManager.addToCart('P1753528795392123', 1);
                updateCartStatus('添加商品结果: ' + result);
                await loadCart();
            } catch (error) {
                updateCartStatus('添加商品错误: ' + error.message);
            }
        }
        
        // 加载购物车
        async function loadCart() {
            if (!window.cartManager) {
                updateCartStatus('CartManager未初始化');
                return;
            }
            
            try {
                await window.cartManager.loadCartFromServer();
                const items = window.cartManager.getCartItems();
                updateCartStatus('购物车商品数量: ' + items.length);
                displayCartItems(items);
            } catch (error) {
                updateCartStatus('加载购物车错误: ' + error.message);
            }
        }
        
        // 清空购物车
        async function clearCart() {
            if (!window.cartManager) {
                updateCartStatus('CartManager未初始化');
                return;
            }
            
            try {
                await window.cartManager.clearCart();
                updateCartStatus('购物车已清空');
                await loadCart();
            } catch (error) {
                updateCartStatus('清空购物车错误: ' + error.message);
            }
        }
        
        // 测试添加API
        async function testAddAPI() {
            const userData = JSON.parse(sessionStorage.getItem('loggedInUser') || '{}');
            if (!userData.username) {
                updateAPIStatus('请先登录');
                return;
            }
            
            try {
                const response = await fetch('/api/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: userData.username,
                        productId: 'P1753528795392123',
                        quantity: 1
                    })
                });
                
                const result = await response.json();
                updateAPIStatus('添加API响应: ' + JSON.stringify(result, null, 2));
            } catch (error) {
                updateAPIStatus('添加API错误: ' + error.message);
            }
        }
        
        // 测试获取API
        async function testGetAPI() {
            const userData = JSON.parse(sessionStorage.getItem('loggedInUser') || '{}');
            if (!userData.username) {
                updateAPIStatus('请先登录');
                return;
            }
            
            try {
                const response = await fetch(`/api/cart?username=${encodeURIComponent(userData.username)}`);
                const result = await response.json();
                updateAPIStatus('获取API响应: ' + JSON.stringify(result, null, 2));
            } catch (error) {
                updateAPIStatus('获取API错误: ' + error.message);
            }
        }
        
        // 更新状态显示
        function updateLoginStatus(message) {
            document.getElementById('login-status').textContent = new Date().toLocaleTimeString() + ': ' + message;
        }
        
        function updateCartStatus(message) {
            document.getElementById('cart-status').textContent = new Date().toLocaleTimeString() + ': ' + message;
        }
        
        function updateAPIStatus(message) {
            document.getElementById('api-status').textContent = new Date().toLocaleTimeString() + ': ' + message;
        }
        
        function displayCartItems(items) {
            const container = document.getElementById('cart-items');
            if (items.length === 0) {
                container.innerHTML = '购物车为空';
            } else {
                container.innerHTML = items.map(item => 
                    `商品ID: ${item.productId}, 数量: ${item.quantity}, 添加时间: ${item.addedAt}`
                ).join('<br>');
            }
        }
        
        // 页面加载时检查状态
        window.addEventListener('load', () => {
            console.log('Page loaded, checking status...');
            checkLoginStatus();

            // 检查CartManager
            if (window.cartManager) {
                updateCartStatus('CartManager已初始化');
                console.log('CartManager found:', window.cartManager);
            } else if (window.initCartManager) {
                updateCartStatus('CartManager未初始化，尝试初始化...');
                window.initCartManager();
                if (window.cartManager) {
                    updateCartStatus('CartManager初始化成功');
                } else {
                    updateCartStatus('CartManager初始化失败');
                }
            } else {
                updateCartStatus('CartManager和initCartManager都未找到');
                console.error('CartManager script may not be loaded properly');
            }
        });
    </script>
</body>
</html>
