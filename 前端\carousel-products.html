<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图商品 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/cart-manager.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 通知动画 */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo-img {
            width: 45px;
            height: 45px;
            margin-right: 10px;
            border-radius: 8px;
        }

        .logo-text h1 {
            font-size: 22px;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 12px;
            color: #666;
        }

        .gold {
            color: #d4af37;
        }

        /* Header Layout */
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #333;
        }

        .header-title h1 {
            font-size: 22px;
            margin-bottom: 2px;
            color: #0c4da2;
        }

        .header-title h1 i {
            color: #ffd700;
            margin-right: 8px;
        }

        .header-title p {
            font-size: 12px;
            color: #666;
            margin: 0;
        }

        .back-button {
            background: linear-gradient(135deg, #0c4da2 0%, #1e5bb8 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(12, 77, 162, 0.3);
            font-size: 14px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(12, 77, 162, 0.4);
        }

        .back-button i {
            margin-right: 8px;
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 150px);
        }

        /* Carousel Banner - 隐藏轮播图信息横幅 */
        .carousel-banner {
            display: none;
        }



        /* Product Grid */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-top: 40px;
        }

        /* Product Card */
        .product-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-image {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .product-overlay {
            opacity: 1;
        }

        .quick-view-btn {
            background: #FFD700;
            color: #333;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-view-btn:hover {
            background: #FFC107;
            transform: scale(1.05);
        }

        .product-info {
            padding: 15px;
        }

        .product-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 8px 0;
            line-height: 1.4;
            height: 44px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .sales-info {
            color: #999;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .current-price {
            font-size: 18px;
            font-weight: 700;
            color: #e74c3c;
        }

        .original-price {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
        }

        .add-to-cart-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-1px);
        }

        /* No Products Message */
        .no-products {
            text-align: center;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 60px 30px;
            border-radius: 12px;
            max-width: 500px;
            margin: 40px auto;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .no-products h2 {
            color: #0c4da2;
            font-size: 28px;
            margin-bottom: 15px;
        }

        .no-products p {
            color: #666;
            font-size: 18px;
            margin-bottom: 20px;
        }

        .no-products i {
            font-size: 48px;
            color: #d4af37;
            margin-bottom: 20px;
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: 60px 20px;
            color: white;
        }

        .loading i {
            font-size: 48px;
            margin-bottom: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Footer */
        footer {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }

        footer .container {
            max-width: 500px;
        }

        footer p {
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .product-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 18px;
            }
        }

        @media (max-width: 768px) {
            .header-title h1 {
                font-size: 20px;
            }



            .product-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }

            .product-image {
                height: 150px;
            }

            .product-name {
                font-size: 14px;
                height: 36px;
            }

            .current-price {
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .header-title {
                display: none;
            }



            .product-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .product-image {
                height: 120px;
            }

            .product-info {
                padding: 10px;
            }

            .product-name {
                font-size: 13px;
                height: 32px;
            }

            .current-price {
                font-size: 15px;
            }

            .add-to-cart-btn {
                padding: 8px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-left">
                <div class="logo">
                    <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                    <div class="logo-text">
                        <h1><span class="gold">金舟</span>国际物流</h1>
                        <p>Jin Zhou International Logistics</p>
                    </div>
                </div>
            </div>

            <div class="header-title">
                <h1><i class="fas fa-images"></i><span id="carouselTitle">轮播图商品</span></h1>
                <p>精选商品，为您推荐最佳选择</p>
            </div>

            <div class="header-right">
                <a href="recommend.html" class="back-button">
                    <i class="fas fa-arrow-left"></i>返回推荐页
                </a>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <!-- 轮播图信息横幅 -->
            <div class="carousel-banner" id="carouselBanner">
                <h1 id="bannerTitle">加载中...</h1>
                <h2 id="bannerSubtitle"></h2>
                <p id="bannerDescription"></p>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loadingState">
                <i class="fas fa-spinner"></i>
                <h3>正在加载商品...</h3>
            </div>

            <!-- 商品网格 -->
            <div class="product-grid" id="productGrid" style="display: none;">
                <!-- 商品卡片将通过JavaScript动态添加 -->
            </div>

            <!-- 无商品提示 -->
            <div class="no-products" id="noProducts" style="display: none;">
                <i class="fas fa-box-open"></i>
                <h2>暂无关联商品</h2>
                <p>该轮播图还没有关联任何商品，请稍后再来查看。</p>
            </div>
        </div>
    </main>

    <footer>
        <div class="container" style="max-width: 800px;">
            <p>&copy; 2023 <span class="gold">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // 轮播图商品页面管理类
        class CarouselProductsPage {
            constructor() {
                this.carouselId = null;
                this.carousel = null;
                this.products = [];
                this.init();
            }

            init() {
                this.getCarouselId();
                this.loadCarouselData();
            }

            getCarouselId() {
                const urlParams = new URLSearchParams(window.location.search);
                this.carouselId = urlParams.get('id');
                
                if (!this.carouselId) {
                    this.showError('未指定轮播图ID');
                    return;
                }
            }

            loadCarouselData() {
                fetch(`http://localhost:8080/api/carousels/${this.carouselId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.carousel = data.carousel;
                        this.updateBanner();
                        this.loadProducts();
                    } else {
                        console.error('加载轮播图失败:', data.message);
                        this.showError('轮播图不存在或已被删除');
                    }
                })
                .catch(error => {
                    console.error('加载轮播图失败:', error);
                    // 使用模拟轮播图数据
                    this.carousel = this.getMockCarousel();
                    if (this.carousel) {
                        this.updateBanner();
                        this.loadProducts();
                    } else {
                        this.showError('轮播图不存在');
                    }
                });
            }



            updateBanner() {
                document.getElementById('carouselTitle').textContent = this.carousel.title;
                document.getElementById('bannerTitle').textContent = this.carousel.title;
                document.getElementById('bannerSubtitle').textContent = this.carousel.subtitle || '';
                document.getElementById('bannerDescription').textContent = this.carousel.description || '';
                
                // 更新页面标题
                document.title = `${this.carousel.title} - 金舟国际物流`;
            }

            loadProducts() {
                if (!this.carousel.products || this.carousel.products.length === 0) {
                    this.showNoProducts();
                    return;
                }

                // 根据轮播图关联的商品ID加载商品详情
                const productIds = this.carousel.products.map(p => typeof p === 'object' ? p.id : p);

                fetch('http://localhost:8080/api/products')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 过滤出轮播图关联的商品
                        const allProducts = data.products || [];
                        const filteredProducts = allProducts.filter(product =>
                            productIds.includes(product.id)
                        );

                        // 转换服务器数据格式为前端需要的格式，确保图片字段正确
                        this.products = filteredProducts.map(product => {
                            // 获取商品图片，优先使用images数组中的第一张图片，然后是mainImage，最后是默认图片
                            let productImage = 'https://via.placeholder.com/250x250/f0f0f0/999999?text=暂无图片';

                            if (product.images && product.images.length > 0) {
                                productImage = product.images[0].url;
                            } else if (product.mainImage) {
                                productImage = product.mainImage;
                            }

                            return {
                                id: product.id,
                                name: product.name,
                                price: parseFloat(product.price),
                                originalPrice: parseFloat(product.price) * 1.3,
                                image: productImage,
                                // 移除评分功能
                                sales: Math.floor(Math.random() * 2000) + 100,
                                description: product.description || '优质商品，值得信赖'
                            };
                        });

                        if (this.products.length === 0) {
                            this.showNoProducts();
                        } else {
                            this.renderProducts();
                        }
                    } else {
                        console.error('加载商品失败:', data.message);
                        this.showError('加载商品失败');
                    }
                })
                .catch(error => {
                    console.error('加载商品失败:', error);
                    // 使用模拟数据
                    this.products = this.getMockProducts(productIds);
                    if (this.products.length === 0) {
                        this.showNoProducts();
                    } else {
                        this.renderProducts();
                    }
                });
            }

            getMockCarousel() {
                // 根据轮播图ID返回对应的模拟数据
                const mockCarousels = {
                    '1753516086571': {
                        id: '1753516086571',
                        title: '232',
                        subtitle: '精选商品推荐',
                        description: '为您精心挑选的优质商品',
                        products: [
                            {
                                id: 'P1753439659536736',
                                name: '衣服',
                                price: 100,
                                image: '/uploads/image-1753439627488-258116668.png'
                            }
                        ]
                    }
                };

                return mockCarousels[this.carouselId] || null;
            }

            getMockProducts(productIds) {
                const mockProducts = [
                    {
                        id: 'P1753439750943763',
                        name: '12312321',
                        price: 3123123.00,
                        originalPrice: 4060059.00,
                        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUwIiBoZWlnaHQ9IjI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjUwIiBoZWlnaHQ9IjI1MCIgZmlsbD0iIzMzNzNkYyIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWVhuWTgTwvdGV4dD48L3N2Zz4=',
                        // 移除评分
                        reviewCount: 1174,
                        description: '高品质商品，值得信赖'
                    },
                    {
                        id: 'P1753439460790172',
                        name: '2',
                        price: 2323.00,
                        originalPrice: 3019.99,
                        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUwIiBoZWlnaHQ9IjI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjUwIiBoZWlnaHQ9IjI1MCIgZmlsbD0iIzZjNzU3ZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWVhuWTgTI8L3RleHQ+PC9zdmc+',
                        // 移除评分
                        reviewCount: 2002,
                        description: '精选商品，品质保证'
                    },
                    {
                        id: 'P1753439659536736',
                        name: '衣服',
                        price: 100.00,
                        originalPrice: 130.00,
                        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                        // 移除评分
                        reviewCount: 500,
                        description: '时尚服装，舒适面料'
                    },
                    {
                        id: '1',
                        name: '夏季清新T恤',
                        price: 89.00,
                        originalPrice: 120.00,
                        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                        // 移除评分
                        reviewCount: 173,
                        description: '夏季必备，清爽舒适'
                    },
                    {
                        id: '3',
                        name: 'iPad Pro 2023',
                        price: 6999.00,
                        originalPrice: 7999.00,
                        image: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                        rating: 4.7,
                        reviewCount: 762,
                        description: '专业平板，创作利器'
                    },
                    {
                        id: '4',
                        name: '无线蓝牙耳机',
                        price: 299.00,
                        originalPrice: 399.00,
                        image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                        // 移除评分
                        reviewCount: 234,
                        description: '高音质，长续航'
                    }
                ];

                return mockProducts.filter(product => productIds.includes(product.id));
            }

            renderProducts() {
                const loadingState = document.getElementById('loadingState');
                const productGrid = document.getElementById('productGrid');
                const noProducts = document.getElementById('noProducts');

                loadingState.style.display = 'none';
                noProducts.style.display = 'none';
                productGrid.style.display = 'grid';

                productGrid.innerHTML = this.products.map(product => this.createProductCard(product)).join('');
            }

            createProductCard(product) {
                const sales = product.reviewCount || Math.floor(Math.random() * 2000) + 100;
                const originalPrice = product.originalPrice || (product.price * 1.3);

                return `
                    <div class="product-card" onclick="viewProductDetail('${product.id}')">
                        <div class="product-image">
                            <img src="${product.image || 'https://via.placeholder.com/250x250/f0f0f0/999999?text=暂无图片'}"
                                 alt="${product.name}" loading="lazy"
                                 onerror="console.log('图片加载失败:', this.src); this.src='https://via.placeholder.com/250x250/f0f0f0/999999?text=暂无图片'"
                                 onload="console.log('图片加载成功:', this.src)">
                            <div class="product-overlay">
                                <button class="quick-view-btn" onclick="event.stopPropagation(); viewProductDetail('${product.id}')">快速查看</button>
                            </div>
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">${product.name}</h3>
                            <div class="sales-info">${sales}人已购买</div>
                            <div class="product-price">
                                <span class="current-price">¥${product.price.toFixed(2)}</span>
                                <span class="original-price">¥${originalPrice.toFixed(2)}</span>
                            </div>
                            <button class="add-to-cart-btn" onclick="event.stopPropagation(); addToCart('${product.id}')">
                                <i class="fas fa-shopping-cart"></i>
                                加入购物车
                            </button>
                        </div>
                    </div>
                `;
            }

            // 移除星星评分生成函数

            showNoProducts() {
                const loadingState = document.getElementById('loadingState');
                const productGrid = document.getElementById('productGrid');
                const noProducts = document.getElementById('noProducts');

                loadingState.style.display = 'none';
                productGrid.style.display = 'none';
                noProducts.style.display = 'block';
            }

            showError(message) {
                const loadingState = document.getElementById('loadingState');
                loadingState.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>${message}</h3>
                    <p><a href="recommend.html" style="color: #FFD700;">返回推荐页面</a></p>
                `;
            }
        }

        // 全局函数
        function viewProductDetail(productId) {
            window.open(`product-detail.html?id=${productId}&from=carousel`, '_blank');
        }

        async function addToCart(productId) {
            if (window.cartManager) {
                const success = await window.cartManager.addToCart(productId, 1);
                if (success) {
                    // 显示成功提示
                    const notification = document.createElement('div');
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #52c41a;
                        color: white;
                        padding: 12px 20px;
                        border-radius: 6px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        z-index: 10000;
                        font-size: 14px;
                        animation: slideInRight 0.3s ease-out;
                    `;
                    notification.innerHTML = `
                        <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
                        商品已添加到购物车！
                    `;

                    document.body.appendChild(notification);

                    // 3秒后自动移除通知
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 3000);
                }
            } else {
                alert('购物车功能暂时不可用，请稍后重试');
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            new CarouselProductsPage();
        });
    </script>
</body>
</html>
